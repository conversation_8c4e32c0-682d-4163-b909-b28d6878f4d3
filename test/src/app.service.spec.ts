import { Test, TestingModule } from '@nestjs/testing';
import { AppService } from '../../src/app.service';

describe('AppService', () => {
  let service: AppService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [AppService],
    }).compile();

    service = module.get<AppService>(AppService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getApiInfo', () => {
    it('should be defined', () => {
      expect(service.getApiInfo).toBeDefined();
    });

    it('should return API information object', () => {
      const result = service.getApiInfo();

      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
    });

    it('should return correct API name', () => {
      const result = service.getApiInfo() as any;

      expect(result.name).toBe('API Kitchen - Vitola Lanches');
    });

    it('should return correct API version', () => {
      const result = service.getApiInfo() as any;

      expect(result.version).toBe('1.0.0');
    });

    it('should return correct API description', () => {
      const result = service.getApiInfo() as any;

      expect(result.description).toBe('API para gerenciamento da cozinha do Vitola Lanches');
    });

    it('should return all required properties', () => {
      const result = service.getApiInfo();

      expect(result).toHaveProperty('name');
      expect(result).toHaveProperty('version');
      expect(result).toHaveProperty('description');
      expect(result).toHaveProperty('endpoints');
      expect(result).toHaveProperty('features');
    });

    it('should return correct endpoints', () => {
      const result = service.getApiInfo() as any;

      expect(result.endpoints).toEqual({
        orders: '/kitchen/orders',
        docs: '/api/docs',
        health: '/health'
      });
    });

    it('should return correct features array', () => {
      const result = service.getApiInfo() as any;

      expect(result.features).toEqual([
        'Listagem de pedidos da cozinha',
        'Alteração de status dos pedidos',
        'Documentação interativa com Swagger'
      ]);
    });

    it('should return features as array', () => {
      const result = service.getApiInfo() as any;

      expect(Array.isArray(result.features)).toBe(true);
      expect(result.features).toHaveLength(3);
    });

    it('should return consistent data on multiple calls', () => {
      const result1 = service.getApiInfo();
      const result2 = service.getApiInfo();

      expect(result1).toEqual(result2);
    });

    it('should return immutable object structure', () => {
      const result1 = service.getApiInfo() as any;
      const result2 = service.getApiInfo() as any;

      // Modify first result
      result1.name = 'Modified Name';
      result1.endpoints.orders = '/modified';

      // Second result should remain unchanged
      expect(result2.name).toBe('API Kitchen - Vitola Lanches');
      expect(result2.endpoints.orders).toBe('/kitchen/orders');
    });
  });

  describe('getHealth', () => {
    it('should be defined', () => {
      expect(service.getHealth).toBeDefined();
    });

    it('should return health check object', () => {
      const result = service.getHealth();

      expect(result).toBeDefined();
      expect(typeof result).toBe('object');
    });

    it('should return all required health properties', () => {
      const result = service.getHealth();

      expect(result).toHaveProperty('status');
      expect(result).toHaveProperty('timestamp');
      expect(result).toHaveProperty('uptime');
      expect(result).toHaveProperty('memory');
      expect(result).toHaveProperty('version');
    });

    it('should return status as ok', () => {
      const result = service.getHealth() as any;

      expect(result.status).toBe('ok');
    });

    it('should return valid timestamp', () => {
      const result = service.getHealth() as any;

      expect(result.timestamp).toBeDefined();
      expect(typeof result.timestamp).toBe('string');
      expect(new Date(result.timestamp).getTime()).not.toBeNaN();
    });

    it('should return current timestamp', () => {
      const beforeCall = new Date();
      const result = service.getHealth() as any;
      const afterCall = new Date();

      const resultTime = new Date(result.timestamp);
      expect(resultTime.getTime()).toBeGreaterThanOrEqual(beforeCall.getTime());
      expect(resultTime.getTime()).toBeLessThanOrEqual(afterCall.getTime());
    });

    it('should return valid uptime', () => {
      const result = service.getHealth() as any;

      expect(result.uptime).toBeDefined();
      expect(typeof result.uptime).toBe('number');
      expect(result.uptime).toBeGreaterThanOrEqual(0);
    });

    it('should return memory usage object', () => {
      const result = service.getHealth() as any;

      expect(result.memory).toBeDefined();
      expect(typeof result.memory).toBe('object');
      expect(result.memory).toHaveProperty('rss');
      expect(result.memory).toHaveProperty('heapTotal');
      expect(result.memory).toHaveProperty('heapUsed');
      expect(result.memory).toHaveProperty('external');
    });

    it('should return valid memory values', () => {
      const result = service.getHealth() as any;

      expect(typeof result.memory.rss).toBe('number');
      expect(typeof result.memory.heapTotal).toBe('number');
      expect(typeof result.memory.heapUsed).toBe('number');
      expect(typeof result.memory.external).toBe('number');
      
      expect(result.memory.rss).toBeGreaterThan(0);
      expect(result.memory.heapTotal).toBeGreaterThan(0);
      expect(result.memory.heapUsed).toBeGreaterThan(0);
    });

    it('should return Node.js version', () => {
      const result = service.getHealth() as any;

      expect(result.version).toBeDefined();
      expect(typeof result.version).toBe('string');
      expect(result.version).toMatch(/^v\d+\.\d+\.\d+/);
    });

    it('should return different timestamps on multiple calls', async () => {
      const result1 = service.getHealth() as any;

      // Wait a small amount to ensure different timestamps
      await new Promise(resolve => setTimeout(resolve, 10));

      const result2 = service.getHealth() as any;

      // Verify that timestamps are valid dates and different
      expect(new Date(result1.timestamp)).toBeInstanceOf(Date);
      expect(new Date(result2.timestamp)).toBeInstanceOf(Date);
      expect(result1.timestamp).not.toBe(result2.timestamp);
    });

    it('should return increasing uptime on multiple calls', async () => {
      const result1 = service.getHealth() as any;
      
      // Wait a small amount
      await new Promise(resolve => setTimeout(resolve, 10));
      
      const result2 = service.getHealth() as any;

      expect(result2.uptime).toBeGreaterThanOrEqual(result1.uptime);
    });
  });

  describe('Service Integration', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should be an instance of AppService', () => {
      expect(service).toBeInstanceOf(AppService);
    });

    it('should handle concurrent calls correctly', async () => {
      const promises = Array.from({ length: 10 }, () => 
        Promise.all([
          service.getApiInfo(),
          service.getHealth()
        ])
      );

      const results = await Promise.all(promises);

      expect(results).toHaveLength(10);
      results.forEach(([apiInfo, health]) => {
        expect(apiInfo).toBeDefined();
        expect(health).toBeDefined();
      });
    });

    it('should maintain performance under load', async () => {
      const startTime = Date.now();
      
      const promises = Array.from({ length: 100 }, () => 
        service.getApiInfo()
      );

      await Promise.all(promises);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete 100 calls in less than 1 second
      expect(duration).toBeLessThan(1000);
    });

    it('should not throw errors on repeated calls', () => {
      expect(() => {
        for (let i = 0; i < 50; i++) {
          service.getApiInfo();
          service.getHealth();
        }
      }).not.toThrow();
    });
  });

  describe('Edge Cases', () => {
    it('should handle getApiInfo without errors', () => {
      expect(() => service.getApiInfo()).not.toThrow();
    });

    it('should handle getHealth without errors', () => {
      expect(() => service.getHealth()).not.toThrow();
    });

    it('should return valid JSON serializable objects', () => {
      const apiInfo = service.getApiInfo();
      const health = service.getHealth();

      expect(() => JSON.stringify(apiInfo)).not.toThrow();
      expect(() => JSON.stringify(health)).not.toThrow();
    });

    it('should return objects that can be safely cloned', () => {
      const apiInfo = service.getApiInfo();
      const health = service.getHealth();

      expect(() => structuredClone(apiInfo)).not.toThrow();
      expect(() => structuredClone(health)).not.toThrow();
    });
  });
});
