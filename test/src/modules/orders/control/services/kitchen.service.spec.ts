import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { KitchenService } from '../../../../../../src/modules/orders/control/services/kitchen.service';
import { OrderRepository } from '../../../../../../src/modules/orders/control/repositories/order.repository';
import { OrderStatus } from '../../../../../../src/modules/orders/models/order-status';
import { OrderDto } from '../../../../../../src/modules/orders/dto/order.dto';
import { OrderFactory } from '../../../../test-utils/factories/order.factory';

describe('KitchenService', () => {
  let service: KitchenService;
  let orderRepository: jest.Mocked<OrderRepository>;

  const mockOrderRepository = {
    findOrders: jest.fn(),
    changeOrderStatus: jest.fn(),
    findOrderById: jest.fn(),
    findOrdersByStatus: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KitchenService,
        {
          provide: OrderRepository,
          useValue: mockOrderRepository,
        },
      ],
    }).compile();

    service = module.get<KitchenService>(KitchenService);
    orderRepository = module.get(OrderRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should have orderRepository injected', () => {
      expect(orderRepository).toBeDefined();
    });
  });

  describe('findOrdersStatus', () => {
    it('should return orders when repository returns data', async () => {
      const mockOrders = [
        OrderFactory.createOrder({ id: '1', status: OrderStatus.RECEIVED }),
        OrderFactory.createOrder({ id: '2', status: OrderStatus.PREPARING }),
      ];
      orderRepository.findOrders.mockResolvedValue(mockOrders);

      const result = await service.findOrdersStatus();

      expect(result).toEqual(mockOrders);
      expect(orderRepository.findOrders).toHaveBeenCalledTimes(1);
    });

    it('should return empty array when no orders found', async () => {
      orderRepository.findOrders.mockResolvedValue([]);

      const result = await service.findOrdersStatus();

      expect(result).toEqual([]);
      expect(orderRepository.findOrders).toHaveBeenCalledTimes(1);
    });

    it('should handle repository errors', async () => {
      const error = new Error('Repository error');
      orderRepository.findOrders.mockRejectedValue(error);

      await expect(service.findOrdersStatus()).rejects.toThrow('Falha ao buscar pedidos da cozinha');
      expect(orderRepository.findOrders).toHaveBeenCalledTimes(1);
    });
  });

  describe('changeOrderStatus', () => {
    const orderId = '507f1f77bcf86cd799439011';
    const newStatus = OrderStatus.PREPARING;

    it('should change order status successfully', async () => {
      orderRepository.changeOrderStatus.mockResolvedValue(true);

      const result = await service.changeOrderStatus(orderId, newStatus);

      expect(result).toBe(true);
      expect(orderRepository.changeOrderStatus).toHaveBeenCalledWith(orderId, newStatus);
    });

    it('should handle invalid order ID', async () => {
      const invalidId = '';

      await expect(service.changeOrderStatus(invalidId, newStatus))
        .rejects.toThrow('Id do pedido é obrigatório');
    });

    it('should handle invalid status', async () => {
      const invalidStatus = null as any;

      await expect(service.changeOrderStatus(orderId, invalidStatus))
        .rejects.toThrow('Status do pedido é obrigatório');
    });

    it('should handle repository errors', async () => {
      const error = new Error('Failed to update order');
      orderRepository.changeOrderStatus.mockRejectedValue(error);

      await expect(service.changeOrderStatus(orderId, newStatus))
        .rejects.toThrow('Falha ao alterar status do pedido');
    });

    it('should handle different order statuses', async () => {
      const statuses = [
        OrderStatus.RECEIVED,
        OrderStatus.PREPARING,
        OrderStatus.READY,
        OrderStatus.COMPLETED,
        OrderStatus.CANCELED,
      ];

      orderRepository.changeOrderStatus.mockResolvedValue(true);

      for (const status of statuses) {
        const result = await service.changeOrderStatus(orderId, status);
        expect(result).toBe(true);
      }

      expect(orderRepository.changeOrderStatus).toHaveBeenCalledTimes(statuses.length);
    });
  });

  describe('findOrderById', () => {
    const orderId = '507f1f77bcf86cd799439011';

    it('should find order by ID successfully', async () => {
      const mockOrder = OrderFactory.createOrder({ id: orderId });
      orderRepository.findOrderById.mockResolvedValue(mockOrder);

      const result = await service.findOrderById(orderId);

      expect(result).toEqual(mockOrder);
      expect(orderRepository.findOrderById).toHaveBeenCalledWith(orderId);
    });

    it('should handle order not found', async () => {
      orderRepository.findOrderById.mockResolvedValue(null);

      const result = await service.findOrderById(orderId);

      expect(result).toBeNull();
      expect(orderRepository.findOrderById).toHaveBeenCalledWith(orderId);
    });

    it('should handle invalid order ID', async () => {
      const invalidId = '';

      await expect(service.findOrderById(invalidId))
        .rejects.toThrow('Id do pedido é obrigatório');
    });

    it('should handle repository errors', async () => {
      const error = new Error('Database error');
      orderRepository.findOrderById.mockRejectedValue(error);

      await expect(service.findOrderById(orderId))
        .rejects.toThrow('Falha ao buscar pedido');
    });
  });

  describe('findOrdersByStatus', () => {
    const status = OrderStatus.PREPARING;

    it('should find orders by status successfully', async () => {
      const mockOrders = [
        OrderFactory.createOrder({ status }),
        OrderFactory.createOrder({ status }),
      ];
      orderRepository.findOrdersByStatus.mockResolvedValue(mockOrders);

      const result = await service.findOrdersByStatus(status);

      expect(result).toEqual(mockOrders);
      expect(orderRepository.findOrdersByStatus).toHaveBeenCalledWith(status);
    });

    it('should return empty array when no orders found for status', async () => {
      orderRepository.findOrdersByStatus.mockResolvedValue([]);

      const result = await service.findOrdersByStatus(status);

      expect(result).toEqual([]);
      expect(orderRepository.findOrdersByStatus).toHaveBeenCalledWith(status);
    });

    it('should handle invalid status', async () => {
      const invalidStatus = null as any;
      orderRepository.findOrdersByStatus.mockResolvedValue([]);

      // O serviço não valida status inválido, apenas passa para o repository
      const result = await service.findOrdersByStatus(invalidStatus);
      expect(result).toEqual([]);
    });

    it('should handle repository errors', async () => {
      const error = new Error('Query failed');
      orderRepository.findOrdersByStatus.mockRejectedValue(error);

      await expect(service.findOrdersByStatus(status))
        .rejects.toThrow('Falha ao buscar pedidos por status');
    });
  });

  describe('error handling', () => {
    it('should handle multiple concurrent requests', async () => {
      const mockOrders = [OrderFactory.createOrder()];
      orderRepository.findOrders.mockResolvedValue(mockOrders);

      const promises = Array.from({ length: 5 }, () => service.findOrdersStatus());
      const results = await Promise.all(promises);

      results.forEach(result => {
        expect(result).toEqual(mockOrders);
      });
      expect(orderRepository.findOrders).toHaveBeenCalledTimes(5);
    });

    it('should handle service unavailable scenarios', async () => {
      const error = new Error('Service unavailable');
      orderRepository.findOrders.mockRejectedValue(error);

      await expect(service.findOrdersStatus()).rejects.toThrow('Falha ao buscar pedidos da cozinha');
    });
  });

  describe('integration scenarios', () => {
    it('should handle complete order workflow', async () => {
      const orderId = '507f1f77bcf86cd799439011';
      const mockOrder = OrderFactory.createOrder({ 
        id: orderId, 
        status: OrderStatus.RECEIVED 
      });

      // Find order
      orderRepository.findOrderById.mockResolvedValue(mockOrder);
      const foundOrder = await service.findOrderById(orderId);
      expect(foundOrder).toEqual(mockOrder);

      // Change status to preparing
      orderRepository.changeOrderStatus.mockResolvedValue(true);
      const statusChanged = await service.changeOrderStatus(orderId, OrderStatus.PREPARING);
      expect(statusChanged).toBe(true);

      // Find orders by status
      const preparingOrders = [{ ...mockOrder, status: OrderStatus.PREPARING }];
      orderRepository.findOrdersByStatus.mockResolvedValue(preparingOrders);
      const ordersByStatus = await service.findOrdersByStatus(OrderStatus.PREPARING);
      expect(ordersByStatus).toEqual(preparingOrders);
    });
  });
});
