import { Test, TestingModule } from '@nestjs/testing';
import { KitchenController } from '../../../../../src/modules/orders/controllers/kitchen.controller';
import { KitchenService } from '../../../../../src/modules/orders/control/services/kitchen.service';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { VitolaException } from '../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderFactory } from '../../../test-utils/factories/order.factory';
import { TestHelpers } from '../../../test-utils/helpers/test.helpers';
import { OrderDto } from 'src/modules/orders/dto/order.dto';

describe('KitchenController', () => {
  let controller: KitchenController;
  let kitchenService: jest.Mocked<KitchenService>;

  beforeEach(async () => {
    const mockKitchenService = TestHelpers.createKitchenServiceMock();

    const module: TestingModule = await Test.createTestingModule({
      controllers: [KitchenController],
      providers: [
        {
          provide: KitchenService,
          useValue: mockKitchenService,
        },
      ],
    }).compile();

    controller = module.get<KitchenController>(KitchenController);
    kitchenService = module.get(KitchenService);
  });

  afterEach(() => {
    TestHelpers.clearAllMocks();
  });

  describe('findOrdersStatus', () => {
    it('should return list of orders successfully', async () => {
      const expectedOrders: OrderDto[] =  [
        OrderFactory.createOrder(),
        OrderFactory.createOrder({ items: [] }),
      ];

      kitchenService.findOrdersStatus.mockResolvedValue(expectedOrders);

      const result = await controller.findOrdersStatus();

      expect(result).toEqual(expectedOrders);
      expect(result).toHaveLength(2);
      expect(kitchenService.findOrdersStatus).toHaveBeenCalledTimes(1);
    });

    it('should return empty array when no orders found', async () => {
      kitchenService.findOrdersStatus.mockResolvedValue([]);

      const result = await controller.findOrdersStatus();

      expect(result).toEqual([]);
      expect(result).toHaveLength(0);
      expect(kitchenService.findOrdersStatus).toHaveBeenCalledTimes(1);
    });

    it('should handle service errors when finding orders', async () => {
      const serviceError = new Error('Database connection error');

      kitchenService.findOrdersStatus.mockRejectedValue(serviceError);

      await expect(controller.findOrdersStatus()).rejects.toThrow(serviceError);
      expect(kitchenService.findOrdersStatus).toHaveBeenCalledTimes(1);
    });

    it('should return orders with different statuses', async () => {
      const expectedOrders: OrderDto[] =  [
        OrderFactory.createOrder({ status: OrderStatus.RECEIVED }),
        OrderFactory.createOrder({ status: OrderStatus.PREPARING }),
        OrderFactory.createOrder({ status: OrderStatus.READY }),
      ];


      kitchenService.findOrdersStatus.mockResolvedValue(expectedOrders);

      const result = await controller.findOrdersStatus();

      expect(result).toEqual(expectedOrders);
      expect(result[0].status).toBe(OrderStatus.RECEIVED);
      expect(result[1].status).toBe(OrderStatus.PREPARING);
      expect(result[2].status).toBe(OrderStatus.READY);
    });

  });

  describe('changeOrderStatus', () => {
    it('should change order status successfully', async () => {
      const orderId = 'valid-order-id';
      const newStatus = OrderStatus.PREPARING;

      kitchenService.changeOrderStatus.mockResolvedValue(true);

      const result = await controller.changeOrderStatus(orderId, newStatus);

      expect(result).toBe(true);
      expect(kitchenService.changeOrderStatus).toHaveBeenCalledWith(orderId, newStatus);
      expect(kitchenService.changeOrderStatus).toHaveBeenCalledTimes(1);
    });

    it('should handle invalid order id', async () => {
      const invalidOrderId = '';
      const status = OrderStatus.PREPARING;
      const validationError = VitolaException.ofValidation('ORDERID_IS_REQUIRED', 'Id do pedido inválido');

      kitchenService.changeOrderStatus.mockRejectedValue(validationError);

      await expect(controller.changeOrderStatus(invalidOrderId, status)).rejects.toThrow(validationError);
      expect(kitchenService.changeOrderStatus).toHaveBeenCalledWith(invalidOrderId, status);
    });

    it('should handle invalid status', async () => {
      const orderId = 'valid-order-id';
      const invalidStatus = null as any;
      const validationError = VitolaException.ofValidation('STATUS_IS_REQUIRED', 'Status do pedido inválido');

      kitchenService.changeOrderStatus.mockRejectedValue(validationError);

      await expect(controller.changeOrderStatus(orderId, invalidStatus)).rejects.toThrow(validationError);
      expect(kitchenService.changeOrderStatus).toHaveBeenCalledWith(orderId, invalidStatus);
    });

    it('should handle order not found', async () => {
      const nonExistentOrderId = 'non-existent-order-id';
      const status = OrderStatus.PREPARING;

      kitchenService.changeOrderStatus.mockResolvedValue(false);

      const result = await controller.changeOrderStatus(nonExistentOrderId, status);

      expect(result).toBe(false);
      expect(kitchenService.changeOrderStatus).toHaveBeenCalledWith(nonExistentOrderId, status);
    });

    it('should handle service errors when changing status', async () => {
      const orderId = 'valid-order-id';
      const status = OrderStatus.PREPARING;
      const serviceError = new Error('Database connection error');

      kitchenService.changeOrderStatus.mockRejectedValue(serviceError);

      await expect(controller.changeOrderStatus(orderId, status)).rejects.toThrow(serviceError);
      expect(kitchenService.changeOrderStatus).toHaveBeenCalledWith(orderId, status);
    });

    it('should change status to all valid statuses', async () => {
      const orderId = 'valid-order-id';
      const statuses = [
        OrderStatus.RECEIVED,
        OrderStatus.PREPARING,
        OrderStatus.READY,
        OrderStatus.COMPLETED,
        OrderStatus.CANCELED,
        OrderStatus.DISCARDED,
      ];

      kitchenService.changeOrderStatus.mockResolvedValue(true);

      for (const status of statuses) {
        const result = await controller.changeOrderStatus(orderId, status);
        expect(result).toBe(true);
        expect(kitchenService.changeOrderStatus).toHaveBeenCalledWith(orderId, status);
      }

      expect(kitchenService.changeOrderStatus).toHaveBeenCalledTimes(statuses.length);
    });
  });

  describe('Controller Integration', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should have kitchenService injected', () => {
      expect(kitchenService).toBeDefined();
    });

    it('should handle concurrent requests', async () => {
      const orderId1 = 'order-1';
      const orderId2 = 'order-2';
      const status = OrderStatus.PREPARING;

      kitchenService.changeOrderStatus.mockResolvedValue(true);

      const [result1, result2] = await Promise.all([
        controller.changeOrderStatus(orderId1, status),
        controller.changeOrderStatus(orderId2, status),
      ]);

      expect(result1).toBe(true);
      expect(result2).toBe(true);
      expect(kitchenService.changeOrderStatus).toHaveBeenCalledTimes(2);
    });
  });
});
