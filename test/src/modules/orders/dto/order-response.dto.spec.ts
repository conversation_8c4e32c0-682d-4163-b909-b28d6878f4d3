import { OrderResponseDto } from '../../../../../src/modules/orders/dto/order-response.dto';
import { OrderStatus } from '../../../../../src/modules/orders/models/order-status';
import { OrderFactory } from '../../../test-utils/factories/order.factory';

describe('OrderResponseDto', () => {
  describe('constructor', () => {
    it('should create OrderResponseDto with all properties', () => {
      const id = 'test-id';
      const orderNumber = 123;
      const orderStatus = OrderStatus.RECEIVED;
      const receivedDate = new Date();
      const lastUpdateDate = new Date();

      const dto = new OrderResponseDto(id, orderNumber, orderStatus, receivedDate, lastUpdateDate);

      expect(dto.id).toBe(id);
      expect(dto.orderNumber).toBe(orderNumber);
      expect(dto.orderStatus).toBe(orderStatus);
      expect(dto.receivedDate).toBe(receivedDate);
      expect(dto.lastUpdateDate).toBe(lastUpdateDate);
    });

    it('should create OrderResponseDto with different order statuses', () => {
      const statuses = [
        OrderStatus.RECEIVED,
        OrderStatus.PREPARING,
        OrderStatus.READY,
        OrderStatus.COMPLETED,
        OrderStatus.CANCELED,
        OrderStatus.DISCARDED,
      ];

      statuses.forEach((status) => {
        const dto = new OrderResponseDto('id', 1, status, new Date(), new Date());
        expect(dto.orderStatus).toBe(status);
      });
    });

    it('should handle different order numbers', () => {
      const orderNumbers = [1, 100, 999, 1000];

      orderNumbers.forEach((orderNumber) => {
        const dto = new OrderResponseDto('id', orderNumber, OrderStatus.RECEIVED, new Date(), new Date());
        expect(dto.orderNumber).toBe(orderNumber);
      });
    });

    it('should handle different date values', () => {
      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);

      const dto = new OrderResponseDto('id', 1, OrderStatus.RECEIVED, yesterday, tomorrow);

      expect(dto.receivedDate).toBe(yesterday);
      expect(dto.lastUpdateDate).toBe(tomorrow);
      expect(dto.lastUpdateDate.getTime()).toBeGreaterThan(dto.receivedDate.getTime());
    });
  });

  describe('of static method', () => {
    it('should create OrderResponseDto from Order entity', () => {
      const order = OrderFactory.createOrder({
        id: 'test-order-id',
        orderNumber: 456,
        status: OrderStatus.PREPARING,
      });

      const dto = OrderResponseDto.of(order);

      expect(dto).toBeInstanceOf(OrderResponseDto);
      expect(dto.id).toBe(order.id);
      expect(dto.orderNumber).toBe(order.orderNumber);
      expect(dto.orderStatus).toBe(order.status);
    });

    it('should handle Order with all different statuses', () => {
      const statuses = [
        OrderStatus.RECEIVED,
        OrderStatus.PREPARING,
        OrderStatus.READY,
        OrderStatus.COMPLETED,
        OrderStatus.CANCELED,
        OrderStatus.DISCARDED,
      ];

      statuses.forEach((status) => {
        const order = OrderFactory.createOrder({ status });
        const dto = OrderResponseDto.of(order);
        expect(dto.orderStatus).toBe(status);
      });
    });

    it('should handle Order with different order numbers', () => {
      const orderNumbers = [1, 50, 100, 500, 1000];

      orderNumbers.forEach((orderNumber) => {
        const order = OrderFactory.createOrder({ orderNumber });
        const dto = OrderResponseDto.of(order);
        expect(dto.orderNumber).toBe(orderNumber);
      });
    });

    it('should preserve date information from Order', () => {
      const order = OrderFactory.createOrder();
      const dto = OrderResponseDto.of(order);

      // Como OrderDto não tem createDate/updateDate, vamos testar que o DTO é criado corretamente
      expect(dto).toBeInstanceOf(OrderResponseDto);
      expect(dto.id).toBe(order.id);
    });

    it('should handle Order with complex data', () => {
      const order = OrderFactory.createOrderWithMultipleItems(5);
      order.id = 'complex-order-id';
      order.orderNumber = 789;
      order.status = OrderStatus.READY;

      const dto = OrderResponseDto.of(order);

      expect(dto.id).toBe('complex-order-id');
      expect(dto.orderNumber).toBe(789);
      expect(dto.orderStatus).toBe(OrderStatus.READY);
    });

    it('should create multiple DTOs from multiple Orders', () => {
      const orders = OrderFactory.createMultipleOrders(3);
      const dtos = orders.map((order) => OrderResponseDto.of(order));

      expect(dtos).toHaveLength(3);
      dtos.forEach((dto, index) => {
        expect(dto).toBeInstanceOf(OrderResponseDto);
        expect(dto.id).toBe(orders[index].id);
        expect(dto.orderNumber).toBe(orders[index].orderNumber);
        expect(dto.orderStatus).toBe(orders[index].status);
      });
    });

    it('should handle Orders with different statuses in batch', () => {
      const orders = OrderFactory.createOrdersWithDifferentStatuses();
      const dtos = orders.map((order) => OrderResponseDto.of(order));

      expect(dtos).toHaveLength(4);
      expect(dtos[0].orderStatus).toBe(OrderStatus.RECEIVED);
      expect(dtos[1].orderStatus).toBe(OrderStatus.PREPARING);
      expect(dtos[2].orderStatus).toBe(OrderStatus.READY);
      expect(dtos[3].orderStatus).toBe(OrderStatus.COMPLETED);
    });
  });

  describe('DTO Properties', () => {
    it('should have all required properties defined', () => {
      const dto = OrderFactory.createOrderResponseDto();

      expect(dto).toHaveProperty('id');
      expect(dto).toHaveProperty('orderNumber');
      expect(dto).toHaveProperty('orderStatus');
      expect(dto).toHaveProperty('receivedDate');
      expect(dto).toHaveProperty('lastUpdateDate');
    });

    it('should have correct property types', () => {
      const dto = OrderFactory.createOrderResponseDto();

      expect(typeof dto.id).toBe('string');
      expect(typeof dto.orderNumber).toBe('number');
      expect(typeof dto.orderStatus).toBe('string');
      expect(dto.receivedDate).toBeInstanceOf(Date);
      expect(dto.lastUpdateDate).toBeInstanceOf(Date);
    });

    it('should handle edge cases for order numbers', () => {
      const edgeCases = [0, 1, Number.MAX_SAFE_INTEGER];

      edgeCases.forEach((orderNumber) => {
        const dto = new OrderResponseDto('id', orderNumber, OrderStatus.RECEIVED, new Date(), new Date());
        expect(dto.orderNumber).toBe(orderNumber);
        expect(typeof dto.orderNumber).toBe('number');
      });
    });

    it('should handle empty string id', () => {
      const dto = new OrderResponseDto('', 1, OrderStatus.RECEIVED, new Date(), new Date());
      expect(dto.id).toBe('');
      expect(typeof dto.id).toBe('string');
    });

    it('should maintain immutability of date objects', () => {
      const originalDate = new Date();
      const dto = new OrderResponseDto('id', 1, OrderStatus.RECEIVED, originalDate, originalDate);

      // Modifying the original date should not affect the DTO
      originalDate.setTime(originalDate.getTime() + 1000);

      // The DTO should still reference the same date object (by design)
      expect(dto.receivedDate).toBe(originalDate);
      expect(dto.lastUpdateDate).toBe(originalDate);
    });
  });
});
