import { Test, TestingModule } from '@nestjs/testing';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { of, throwError } from 'rxjs';
import { AxiosResponse, AxiosError } from 'axios';
import { HttpClientService } from '../../../../../src/modules/shared/http/http-client.service';
import { createMockHttpService, createMockConfigService } from '../../../test-utils/mocks/mongoose.mock';

describe('HttpClientService', () => {
  let service: HttpClientService;
  let httpService: jest.Mocked<HttpService>;
  let configService: jest.Mocked<ConfigService>;

  const mockResponse = <T>(data: T): AxiosResponse<T> => ({
    data: { return: data } as any,
    status: 200,
    statusText: 'OK',
    headers: {},
    config: {} as any,
  });

  beforeEach(async () => {
    const mockHttpService = createMockHttpService();
    const mockConfigService = createMockConfigService();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HttpClientService,
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<HttpClientService>(HttpClientService);
    httpService = module.get(HttpService);
    configService = module.get(ConfigService);

    // Mock environment variable
    process.env.ORDERS_API_URL = 'http://test-api:3001';
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should be defined', () => {
      expect(service).toBeDefined();
    });

    it('should configure orders API URL from environment', () => {
      expect(process.env.ORDERS_API_URL).toBe('http://test-api:3001');
    });
  });

  describe('get', () => {
    it('should make GET request and return data', (done) => {
      const testData = { id: 1, name: 'test' };
      httpService.get.mockReturnValue(of(mockResponse(testData)));

      service.get('/test').subscribe({
        next: (result) => {
          expect(result).toEqual(testData);
          expect(httpService.get).toHaveBeenCalledWith('http://test-api:3001/test', undefined);
          done();
        },
      });
    });

    it('should handle GET request with config', (done) => {
      const testData = { id: 1 };
      const config = { headers: { 'Custom-Header': 'value' } };
      httpService.get.mockReturnValue(of(mockResponse(testData)));

      service.get('/test', config).subscribe({
        next: (result) => {
          expect(result).toEqual(testData);
          expect(httpService.get).toHaveBeenCalledWith('http://test-api:3001/test', config);
          done();
        },
      });
    });

    it('should handle GET request errors', (done) => {
      const error = new Error('Network error');
      httpService.get.mockReturnValue(throwError(() => error));

      service.get('/test').subscribe({
        error: (err) => {
          expect(err).toBeDefined();
          done();
        },
      });
    });
  });

  describe('post', () => {
    it('should make POST request and return data', (done) => {
      const testData = { success: true };
      const postData = { name: 'test' };
      httpService.post.mockReturnValue(of(mockResponse(testData)));

      service.post('/test', postData).subscribe({
        next: (result) => {
          expect(result).toEqual(testData);
          expect(httpService.post).toHaveBeenCalledWith('http://test-api:3001/test', postData, undefined);
          done();
        },
      });
    });

    it('should handle POST request with config', (done) => {
      const testData = { success: true };
      const postData = { name: 'test' };
      const config = { timeout: 1000 };
      httpService.post.mockReturnValue(of(mockResponse(testData)));

      service.post('/test', postData, config).subscribe({
        next: (result) => {
          expect(result).toEqual(testData);
          expect(httpService.post).toHaveBeenCalledWith('http://test-api:3001/test', postData, config);
          done();
        },
      });
    });

    it('should handle POST request errors', (done) => {
      const error = new Error('Bad request');
      httpService.post.mockReturnValue(throwError(() => error));

      service.post('/test', {}).subscribe({
        error: (err) => {
          expect(err).toBeDefined();
          done();
        },
      });
    });
  });

  describe('put', () => {
    it('should make PUT request and return data', (done) => {
      const testData = { updated: true };
      const putData = { id: 1, name: 'updated' };
      httpService.put.mockReturnValue(of(mockResponse(testData)));

      service.put('/test', putData).subscribe({
        next: (result) => {
          expect(result).toEqual(testData);
          expect(httpService.put).toHaveBeenCalledWith('http://test-api:3001/test', putData, undefined);
          done();
        },
      });
    });

    it('should handle PUT request with config', (done) => {
      const testData = { updated: true };
      const putData = { id: 1 };
      const config = { headers: { 'Authorization': 'Bearer token' } };
      httpService.put.mockReturnValue(of(mockResponse(testData)));

      service.put('/test', putData, config).subscribe({
        next: (result) => {
          expect(result).toEqual(testData);
          expect(httpService.put).toHaveBeenCalledWith('http://test-api:3001/test', putData, config);
          done();
        },
      });
    });

    it('should handle PUT request errors', (done) => {
      const error = new Error('Update failed');
      httpService.put.mockReturnValue(throwError(() => error));

      service.put('/test', {}).subscribe({
        error: (err) => {
          expect(err).toBeDefined();
          done();
        },
      });
    });
  });

  describe('patch', () => {
    it('should make PATCH request and return data', (done) => {
      const testData = { patched: true };
      const patchData = { status: 'active' };
      httpService.patch.mockReturnValue(of(mockResponse(testData)));

      service.patch('/test', patchData).subscribe({
        next: (result) => {
          expect(result).toEqual(testData);
          expect(httpService.patch).toHaveBeenCalledWith('http://test-api:3001/test', patchData, undefined);
          done();
        },
      });
    });
  });

  describe('delete', () => {
    it('should make DELETE request and return data', (done) => {
      const testData = { deleted: true };
      httpService.delete.mockReturnValue(of(mockResponse(testData)));

      service.delete('/test').subscribe({
        next: (result) => {
          expect(result).toEqual(testData);
          expect(httpService.delete).toHaveBeenCalledWith('http://test-api:3001/test', undefined);
          done();
        },
      });
    });
  });

  describe('healthCheck', () => {
    it('should perform health check successfully', async () => {
      const mockHealthResponse = { status: 200, data: { status: 'ok' } } as any;
      httpService.get.mockReturnValue(of(mockHealthResponse));

      const result = await service.healthCheck();

      expect(result).toBe(true);
      expect(httpService.get).toHaveBeenCalledWith('http://test-api:3001/health');
    });

    it('should handle health check failure', async () => {
      httpService.get.mockReturnValue(throwError(() => new Error('Service unavailable')));

      const result = await service.healthCheck();

      expect(result).toBe(false);
    });
  });

  describe('getBaseUrl', () => {
    it('should return configured base URL', () => {
      const result = service.getBaseUrl();
      expect(result).toBe('http://test-api:3001');
    });
  });

  describe('error handling', () => {
    it('should handle network errors', (done) => {
      const networkError = new Error('Network Error');
      httpService.get.mockReturnValue(throwError(() => networkError));

      service.get('/test').subscribe({
        error: (err) => {
          expect(err.method).toBe('GET');
          expect(err.url).toBe('http://test-api:3001/test');
          expect(err.message).toBeDefined();
          done();
        },
      });
    });

    it('should handle HTTP errors with status codes', (done) => {
      const axiosError = {
        response: {
          status: 404,
          statusText: 'Not Found',
          data: { message: 'Resource not found' },
        },
        message: 'Request failed with status code 404',
      } as AxiosError;

      httpService.get.mockReturnValue(throwError(() => axiosError));

      service.get('/not-found').subscribe({
        error: (err) => {
          expect(err.method).toBe('GET');
          expect(err.url).toBe('http://test-api:3001/not-found');
          expect(err.statusCode).toBe(404);
          done();
        },
      });
    });
  });
});
