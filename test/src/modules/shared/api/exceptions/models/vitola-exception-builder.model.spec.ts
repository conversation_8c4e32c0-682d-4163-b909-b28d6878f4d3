import { HttpStatus } from '@nestjs/common';
import { VitolaException } from '../../../../../../../src/modules/shared/api/exceptions/models/vitola-exception-builder.model';

describe('VitolaException', () => {
  describe('constructor', () => {
    it('should create instance with default message', () => {
      const exception = new VitolaException();

      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.message).toBe('');
      // O status padrão do HttpException é 200, mas statusCode não é inicializado
      expect(typeof exception.getStatus()).toBe('number');
    });

    it('should create instance with custom message', () => {
      const message = 'Custom error message';
      const exception = new VitolaException(message);
      
      expect(exception.message).toBe(message);
    });
  });

  describe('static factory methods', () => {
    describe('ofValidation', () => {
      it('should create validation exception with required parameters', () => {
        const errorCode = 'VALIDATION_ERROR';
        const errorMessage = 'Invalid input data';
        
        const exception = VitolaException.ofValidation(errorCode, errorMessage);
        
        expect(exception).toBeInstanceOf(VitolaException);
        expect(exception.message).toBe(errorMessage);
        expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
      });

      it('should create validation exception with all parameters', () => {
        const errorCode = 'VALIDATION_ERROR';
        const errorMessage = 'Invalid input data';
        const contextName = 'OrderController';
        const traceId = 'trace-123';
        
        const exception = VitolaException.ofValidation(errorCode, errorMessage, contextName, traceId);
        
        expect(exception).toBeInstanceOf(VitolaException);
        expect(exception.message).toBe(errorMessage);
        expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
      });
    });

    describe('ofError', () => {
      it('should create internal server error exception', () => {
        const errorCode = 'INTERNAL_ERROR';
        const errorMessage = 'Something went wrong';
        
        const exception = VitolaException.ofError(errorCode, errorMessage);
        
        expect(exception).toBeInstanceOf(VitolaException);
        expect(exception.message).toBe(errorMessage);
        expect(exception.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      });

      it('should create error exception with context and trace', () => {
        const errorCode = 'DATABASE_ERROR';
        const errorMessage = 'Database connection failed';
        const contextName = 'OrderRepository';
        const traceId = 'trace-456';
        
        const exception = VitolaException.ofError(errorCode, errorMessage, contextName, traceId);
        
        expect(exception).toBeInstanceOf(VitolaException);
        expect(exception.message).toBe(errorMessage);
        expect(exception.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
      });
    });

    describe('ofNotFound', () => {
      it('should create not found exception', () => {
        const errorCode = 'NOT_FOUND';
        const errorMessage = 'Resource not found';
        
        const exception = VitolaException.ofNotFound(errorCode, errorMessage);
        
        expect(exception).toBeInstanceOf(VitolaException);
        expect(exception.message).toBe(errorMessage);
        expect(exception.getStatus()).toBe(HttpStatus.NOT_FOUND);
      });

      it('should create not found exception with all parameters', () => {
        const errorCode = 'ORDER_NOT_FOUND';
        const errorMessage = 'Order with ID 123 not found';
        const contextName = 'OrderService';
        const traceId = 'trace-789';
        
        const exception = VitolaException.ofNotFound(errorCode, errorMessage, contextName, traceId);
        
        expect(exception).toBeInstanceOf(VitolaException);
        expect(exception.message).toBe(errorMessage);
        expect(exception.getStatus()).toBe(HttpStatus.NOT_FOUND);
      });
    });
  });

  describe('builder methods', () => {
    let exception: VitolaException;

    beforeEach(() => {
      exception = new VitolaException();
    });

    it('should set error code', () => {
      const errorCode = 'TEST_ERROR';
      const result = exception.withErrorCode(errorCode);
      
      expect(result).toBe(exception); // Should return same instance for chaining
    });

    it('should set message', () => {
      const message = 'Test message';
      const result = exception.withMessage(message);
      
      expect(result).toBe(exception);
      expect(exception.message).toBe(message);
    });

    it('should set HTTP status', () => {
      const status = HttpStatus.FORBIDDEN;
      const result = exception.withHttpStatus(status);
      
      expect(result).toBe(exception);
      expect(exception.getStatus()).toBe(status);
    });

    it('should set context name', () => {
      const contextName = 'TestController';
      const result = exception.withContextName(contextName);
      
      expect(result).toBe(exception);
    });

    it('should set trace ID', () => {
      const traceId = 'trace-test-123';
      const result = exception.withTrace(traceId);
      
      expect(result).toBe(exception);
    });

    it('should set response detail', () => {
      const responseDetail = {
        status: 400,
        statusMessage: 'Bad Request',
        headers: { 'Content-Type': 'application/json' },
        data: { error: 'Invalid data' }
      };
      const result = exception.withResponse(responseDetail);

      expect(result).toBe(exception);
    });

    it('should set data', () => {
      const data = { orderId: '123', userId: '456' };
      const result = exception.withData(data);
      
      expect(result).toBe(exception);
    });
  });

  describe('method chaining', () => {
    it('should allow method chaining', () => {
      const exception = new VitolaException()
        .withErrorCode('CHAIN_TEST')
        .withMessage('Chaining test')
        .withHttpStatus(HttpStatus.BAD_REQUEST)
        .withContextName('TestContext')
        .withTrace('trace-chain-123')
        .withData({ test: true });
      
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.message).toBe('Chaining test');
      expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
    });
  });

  describe('inheritance', () => {
    it('should extend HttpException', () => {
      const exception = new VitolaException();
      
      expect(exception).toBeInstanceOf(Error);
      expect(exception).toBeInstanceOf(VitolaException);
    });

    it('should maintain prototype chain', () => {
      const exception = new VitolaException();
      
      expect(Object.getPrototypeOf(exception)).toBe(VitolaException.prototype);
    });
  });

  describe('error scenarios', () => {
    it('should handle undefined parameters gracefully', () => {
      expect(() => {
        VitolaException.ofValidation(undefined as any, undefined as any);
      }).not.toThrow();
    });

    it('should handle null parameters gracefully', () => {
      expect(() => {
        VitolaException.ofError(null as any, null as any);
      }).not.toThrow();
    });

    it('should handle empty string parameters', () => {
      const exception = VitolaException.ofNotFound('', '');
      
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.message).toBe('');
    });
  });

  describe('complex scenarios', () => {
    it('should create complex validation exception', () => {
      const exception = VitolaException.ofValidation(
        'COMPLEX_VALIDATION',
        'Multiple validation errors occurred'
      )
        .withData({
          errors: [
            { field: 'email', message: 'Invalid email format' },
            { field: 'age', message: 'Age must be positive' }
          ]
        })
        .withResponse({
          status: 400,
          statusMessage: 'Validation Failed',
          headers: { 'X-Error-Count': '2' },
          data: { timestamp: new Date().toISOString() }
        });
      
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.getStatus()).toBe(HttpStatus.BAD_REQUEST);
    });

    it('should create complex error with full context', () => {
      const exception = VitolaException.ofError(
        'DATABASE_CONNECTION_FAILED',
        'Unable to connect to database after 3 retries',
        'OrderRepository',
        'req-123-trace-456'
      )
        .withData({
          retryCount: 3,
          lastError: 'Connection timeout',
          timestamp: Date.now()
        });
      
      expect(exception).toBeInstanceOf(VitolaException);
      expect(exception.getStatus()).toBe(HttpStatus.INTERNAL_SERVER_ERROR);
    });
  });
});
