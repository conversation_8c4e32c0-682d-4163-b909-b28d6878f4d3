export const createMockModel = () => ({
  find: jest.fn().mockReturnThis(),
  findOne: jest.fn().mockReturnThis(),
  findById: jest.fn().mockReturnThis(),
  create: jest.fn(),
  updateOne: jest.fn(),
  updateMany: jest.fn(),
  deleteOne: jest.fn(),
  deleteMany: jest.fn(),
  sort: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  skip: jest.fn().mockReturnThis(),
  populate: jest.fn().mockReturnThis(),
  exec: jest.fn(),
  lean: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  where: jest.fn().mockReturnThis(),
  countDocuments: jest.fn(),
  aggregate: jest.fn(),
  save: jest.fn(),
  remove: jest.fn(),
  new: jest.fn().mockImplementation((doc) => ({
    ...doc,
    save: jest.fn().mockResolvedValue(doc),
    remove: jest.fn().mockResolvedValue(doc),
  })),
});

export const mockOrderModel = createMockModel();

/**
 * Mock completo para HttpClientService que evita chamadas HTTP reais
 */
export const createMockHttpClientService = () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  patch: jest.fn(),
  delete: jest.fn(),
  healthCheck: jest.fn().mockResolvedValue(true),
  getBaseUrl: jest.fn().mockReturnValue('http://mock-api:3001'),
});

/**
 * Mock para HttpService do @nestjs/axios
 */
export const createMockHttpService = () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  patch: jest.fn(),
  delete: jest.fn(),
  head: jest.fn(),
  options: jest.fn(),
  request: jest.fn(),
});

/**
 * Mock para ConfigService
 */
export const createMockConfigService = () => ({
  get: jest.fn().mockImplementation((key: string, defaultValue?: any) => {
    const mockConfig = {
      'ORDERS_API_URL': 'http://mock-orders-api:3001',
      'HTTP_TIMEOUT': 5000,
    };
    return mockConfig[key] || defaultValue;
  }),
  set: jest.fn(),
  has: jest.fn().mockReturnValue(true),
});
