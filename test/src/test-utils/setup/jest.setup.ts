import '../matchers/custom-matchers';

// Configurações globais para os testes
beforeEach(() => {
  // Limpa todos os mocks antes de cada teste
  jest.clearAllMocks();
});

afterEach(() => {
  // Restaura todas as implementações mockadas após cada teste
  jest.restoreAllMocks();
});

// Mock global para console para evitar logs desnecessários durante os testes
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock para variáveis de ambiente padrão
process.env = {
  ...process.env,
  NODE_ENV: 'test',
  MONGO_URI: 'mongodb://localhost:27017/test',
  MERCADO_PAGO_ACCESSTOKEN: 'test-token',
};

// Configuração para timeouts de testes assíncronos
jest.setTimeout(10000);

// Mock para Date.now() para testes determinísticos
const mockDate = new Date('2024-01-01T00:00:00.000Z');
Date.now = jest.fn(() => mockDate.getTime());
