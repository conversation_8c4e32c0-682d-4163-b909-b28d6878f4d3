# Configuração avançada do SonarQube para API Kitchen
# Este arquivo contém configurações adicionais para análise de qualidade

# Configurações de qualidade de código
quality_gates:
  coverage_threshold: 80
  duplicated_lines_density: 3
  maintainability_rating: A
  reliability_rating: A
  security_rating: A

# Regras específicas para TypeScript/NestJS
rules:
  typescript:
    - no-unused-vars: error
    - no-console: warn
    - prefer-const: error
    - no-var: error
  
  nestjs:
    - injectable-should-be-decorated: error
    - controller-should-be-decorated: error
    - module-should-be-decorated: error

# Exclusões específicas
exclusions:
  files:
    - "**/*.spec.ts"
    - "**/*.e2e-spec.ts"
    - "**/test/**"
    - "**/dist/**"
    - "**/node_modules/**"
    - "**/coverage/**"
    - "src/main.ts"
  
  coverage:
    - "**/*.spec.ts"
    - "**/*.e2e-spec.ts"
    - "**/test/**"
    - "src/main.ts"
    - "**/*.module.ts"

# Configurações de métricas
metrics:
  complexity:
    max_function_complexity: 10
    max_file_complexity: 50
  
  size:
    max_function_lines: 50
    max_file_lines: 300
  
  maintainability:
    max_debt_ratio: 5

# Configurações de segurança
security:
  hotspots:
    - sql-injection
    - xss
    - path-traversal
    - command-injection
  
  vulnerabilities:
    - weak-cryptography
    - hardcoded-credentials
    - insecure-random
