# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build outputs
dist
build
.build
mocky-orders

# Testing
coverage
test-report.xml
.nyc_output

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Git
.git
.gitignore

# Documentation
README.md
CHANGELOG.md
*.md

# SonarQube
.sonarqube
sonar-project.properties
sonar-manager.sh
run_sonar.sh

# Docker
Dockerfile
docker-compose*.yml
.dockerignore

# Scripts
build_app.sh
debug-build.sh

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Backup files
*.backup
*.bak
*.tmp

# GitHub Actions
.github

# Additional test files
jest.config.js
jest-sonar-reporter.config.js
test-modules.js
