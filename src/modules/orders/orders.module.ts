import { Modu<PERSON> } from '@nestjs/common';
import { KitchenController } from './controllers/kitchen.controller';
import { KitchenService } from './control/services/kitchen.service';
import { OrderRepository } from './control/repositories/order.repository';
import { HttpModule } from '../shared/http/http.module';

@Module({
  controllers: [KitchenController],
  providers: [KitchenService, OrderRepository],
  imports: [HttpModule]
})
export class OrdersModule {}
