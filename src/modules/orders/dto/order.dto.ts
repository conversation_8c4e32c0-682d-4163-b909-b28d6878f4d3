import { ApiProperty } from '@nestjs/swagger';
import { VitolaException } from '../../shared/api/exceptions/models/vitola-exception-builder.model';
import { generateNewObjectId } from '../../shared/database/helpers/generate-objectId';
import { OrderStatus } from '../models/order-status';
import { OrderItemDto } from './order-item.dto';
import { PaymentInformation } from '../models/order-payment-information';
import { generateSecureNumericOrderNumber } from '../../shared/utils/order-number-generator';

export class OrderDto {
  id: string;

  @ApiProperty({
    example: '12345678900',
    required: false,
    description: 'Documento do usuário',
  })
  document?: string;

  @ApiProperty({
    required: true,
    description: 'Itens do pedido',
    example: [OrderItemDto],
  })
  items: OrderItemDto[];
  paymentInformation: PaymentInformation;

  @ApiProperty({
    example: 10,
    description: 'Valor total do pedido',
    required: true,
  })
  total: number;
  status: OrderStatus;
  orderNumber: number;

  static validate(order: OrderDto) {
    if (!order?.items?.length) {
      throw VitolaException.ofValidation('ITEMS_NOT_FOUND', 'Items do pedido são obrigatórios.');
    }

    order.id = `${generateNewObjectId()}`;
    order.items.forEach((i) => OrderItemDto.validate(i));

    order.status = OrderStatus.RECEIVED;
    order.orderNumber = generateSecureNumericOrderNumber();

    order.total = OrderDto.calculateTotal(order);
  }

  static validatePayment(order: OrderDto) {
    if (!order.paymentInformation) {
      throw VitolaException.ofValidation('PAYMENT_NOT_FOUND', 'Pagamento do pedido é obrigatório.');
    }

    PaymentInformation.validate(order.paymentInformation);
  }

  static calculateTotal(order: OrderDto): number {
    let orderTotal = 0;
    order.items.forEach((p) => {
      orderTotal += p.quantity * p.unitPrice;
    });

    return orderTotal;
  }
}
