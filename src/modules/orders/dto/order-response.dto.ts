import { ApiProperty } from '@nestjs/swagger';
import { OrderStatus } from '../models/order-status';

export class OrderResponseDto {
  @ApiProperty({
    description: 'Id do pedido',
    example: '507f1f77bcf86cd799439011',
  })
  id: string;

  @ApiProperty({
    description: 'Id interno do pedido (MongoDB)',
    example: '507f1f77bcf86cd799439011',
  })
  _id?: string;

  @ApiProperty({
    description: 'Número do pedido',
    example: 123,
  })
  orderNumber: number;

  @ApiProperty({
    description: 'Status do pedido',
    example: OrderStatus.RECEIVED,
    enum: OrderStatus,
  })
  orderStatus: OrderStatus;

  @ApiProperty({
    description: 'Data de recebimento do pedido',
    example: new Date(),
  })
  receivedDate: Date;

  @ApiProperty({
    description: 'Última data de atualização do pedido',
    example: new Date(),
  })
  lastUpdateDate: Date;

  @ApiProperty({
    description: 'Documento do cliente',
    example: '123.456.789-00',
  })
  document?: string;

  @ApiProperty({
    description: 'Data de criação do pedido',
    example: new Date(),
  })
  createDate?: Date;

  @ApiProperty({
    description: 'Data de atualização do pedido',
    example: new Date(),
  })
  updateDate?: Date;

  @ApiProperty({
    description: 'Status do pedido',
    example: OrderStatus.RECEIVED,
    enum: OrderStatus,
  })
  status?: OrderStatus;

  constructor(id: string, orderNumber: number, orderStatus: OrderStatus, receivedDate: Date, lastUpdateDate: Date) {
    this.id = id;
    this.orderNumber = orderNumber;
    this.orderStatus = orderStatus;
    this.receivedDate = receivedDate;
    this.lastUpdateDate = lastUpdateDate;
  }

  static of(order: any): OrderResponseDto {
    const now = new Date();
    return new OrderResponseDto(
      order.id || order._id,
      order.orderNumber,
      order.status,
      order.createDate || now,
      order.updateDate || now
    );
  }
}
