import { Injectable, Logger } from '@nestjs/common';
import { OrderResponseDto } from '../../dto/order-response.dto';
import { OrderStatus } from '../../models/order-status';
import { VitolaException } from 'src/modules/shared/api/exceptions/models/vitola-exception-builder.model';
import { OrderRepository } from '../repositories/order.repository';
import { OrderDto } from '../../dto/order.dto';

@Injectable()
export class KitchenService {
  private readonly logger = new Logger(KitchenService.name);

  constructor(private readonly orderRepository: OrderRepository) { }

  /**
   * Busca todos os pedidos para a cozinha
   * @returns Lista de pedidos
   */
  async findOrdersStatus(): Promise<OrderDto[]> {
    try {
      this.logger.debug('Fetching orders status from orders API');
      const orders = await this.orderRepository.findOrders();

      if (!orders.length) {
        this.logger.debug('No orders found');
        return [];
      }

      // const response = orders.map((order) => this.mapToOrderResponseDto(order));
      this.logger.debug(`Returning ${orders.length} orders`);
      return orders;
    } catch (error) {
      this.logger.error('Failed to fetch orders status:', error);
      throw VitolaException.ofError('FETCH_ORDERS_FAILED', 'Falha ao buscar pedidos da cozinha');
    }
  }

  /**
   * Busca pedidos por status específico
   * @param status Status dos pedidos a buscar
   * @returns Lista de pedidos com o status especificado
   */
  async findOrdersByStatus(status: OrderStatus): Promise<OrderDto[]> {
    try {
      this.logger.debug(`Fetching orders with status ${status}`);
      const orders = await this.orderRepository.findOrdersByStatus(status);

      // const response = orders.map((order) => this.mapToOrderResponseDto(order));
      this.logger.debug(`Found ${orders.length} orders with status ${status}`);
      return orders;
    } catch (error) {
      this.logger.error(`Failed to fetch orders by status ${status}:`, error);
      throw VitolaException.ofError('FETCH_ORDERS_BY_STATUS_FAILED', 'Falha ao buscar pedidos por status');
    }
  }

  /**
   * Busca um pedido específico por ID
   * @param orderId ID do pedido
   * @returns Pedido encontrado ou null
   */
  async findOrderById(orderId: string): Promise<OrderDto > {
    if (!orderId) {
      throw VitolaException.ofValidation('ORDERID_IS_REQUIRED', 'Id do pedido é obrigatório');
    }

    try {
      this.logger.debug(`Fetching order ${orderId}`);
      const order = await this.orderRepository.findOrderById(orderId);

      if (!order) {
        this.logger.debug(`Order ${orderId} not found`);
        return null;
      }

      return order;
    } catch (error) {
      this.logger.error(`Failed to fetch order ${orderId}:`, error);
      throw VitolaException.ofError('FETCH_ORDER_FAILED', 'Falha ao buscar pedido');
    }
  }

  /**
   * Altera o status de um pedido
   * @param orderId ID do pedido
   * @param status Novo status do pedido
   * @returns true se o pedido foi atualizado com sucesso
   */
  async changeOrderStatus(orderId: string, status: OrderStatus): Promise<boolean> {
    if (!orderId) {
      throw VitolaException.ofValidation('ORDERID_IS_REQUIRED', 'Id do pedido é obrigatório');
    }

    if (!status) {
      throw VitolaException.ofValidation('STATUS_IS_REQUIRED', 'Status do pedido é obrigatório');
    }

    try {
      this.logger.debug(`Changing order ${orderId} status to ${status}`);
      const result = await this.orderRepository.changeOrderStatus(orderId, status);

      if (result) {
        this.logger.debug(`Order ${orderId} status changed successfully to ${status}`);
      } else {
        this.logger.warn(`Failed to change order ${orderId} status to ${status}`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Failed to change order ${orderId} status:`, error);
      throw VitolaException.ofError('CHANGE_ORDER_STATUS_FAILED', 'Falha ao alterar status do pedido');
    }
  }

  /**
   * Mapeia OrderDto para OrderResponseDto
   * @param order OrderDto da API de pedidos
   * @returns OrderResponseDto para resposta da API
   */
//   private mapToOrderResponseDto(order: OrderDto): OrderResponseDto {
//     console.log(order);
//     return {
//   _id: order._id,
//   orderNumber: order.orderNumber,
//   orderStatus: order.status,
//   receivedDate: order.createDate,
//   lastUpdateDate: order.updateDate,
//   document: order.document,
//   createDate: order.createDate,
//   updateDate: order.updateDate,
//   status: order.status,
//   id: ''
// };
  // }
}
