import { Injectable, Logger } from "@nestjs/common";
import { OrderStatus } from "../../models/order-status";
import { HttpClientService } from "../../../shared/http/http-client.service";
import { firstValueFrom } from "rxjs";
import { OrderDto } from "../../dto/order.dto";
import axios from 'axios';

@Injectable()
export class OrderRepository {
  private readonly logger = new Logger(OrderRepository.name);

  constructor(private readonly httpClient: HttpClientService) {}

  /**
   * Busca todos os pedidos da API de pedidos
   */
  async findOrders(): Promise<OrderDto[]> {
    try {
      this.logger.debug('Fetching orders from orders API');
     
      return await axios.get(`${process.env.ORDERS_API_URL}/orders`)
      .then(response => {
        return response.data['return'];
      })
      .catch(error => {
        this.logger.error('Failed to fetch orders:', error);
        return null;
      });
       
    } catch (error) {
      this.logger.error('Failed to fetch orders:', error);
      throw new Error(`Failed to fetch orders: ${error.message}`);
    }
  }

  /**
   * Altera o status de um pedido na API de pedidos
   */
  async changeOrderStatus(orderId: string, status: OrderStatus): Promise<boolean> {
    try {
      this.logger.debug(`Changing order ${orderId} status to ${status}`);

      return await axios.put(`${process.env.ORDERS_API_URL}/orders/${orderId}/status/${status}`, {  })
      .then(response => {
        return response.data['return'];
      })
      .catch(error => {
        this.logger.error('Failed to change order status:', error);
        return null;
      });
    } catch (error) {
      this.logger.error(`Failed to change order ${orderId} status:`, error);
      throw new Error(`Failed to change order status: ${error.message}`);
    }
  }

  /**
   * Busca um pedido específico por ID
   */
  async findOrderById(orderId: string): Promise<OrderDto | null> {
    try {
      this.logger.debug(`Fetching order ${orderId} from orders API`);

      const order = await firstValueFrom(
        this.httpClient.get<OrderDto>(`/orders/${orderId}`)
      );

      this.logger.debug(`Found order ${orderId}`);
      return order;
    } catch (error) {
      if (error.statusCode === 404) {
        this.logger.debug(`Order ${orderId} not found`);
        return null;
      }

      this.logger.error(`Failed to fetch order ${orderId}:`, error);
      throw new Error(`Failed to fetch order: ${error.message}`);
    }
  }

  /**
   * Busca pedidos por status
   */
  async findOrdersByStatus(status: OrderStatus): Promise<OrderDto[]> {
    try {
      this.logger.debug(`Fetching orders with status ${status} from orders API`);

      const orders = await firstValueFrom(
        this.httpClient.get<OrderDto[]>(`/orders?status=${status}`)
      );

      this.logger.debug(`Found ${orders.length} orders with status ${status}`);
      return orders;
    } catch (error) {
      this.logger.error(`Failed to fetch orders by status ${status}:`, error);
      throw new Error(`Failed to fetch orders by status: ${error.message}`);
    }
  }
}
