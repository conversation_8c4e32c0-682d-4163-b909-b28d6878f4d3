import { generateNewObjectId } from '../../shared/database/helpers/generate-objectId';
import { PaymentMethod } from '../dto/order-payment-method';
import { OrderResponseDto } from '../dto/order-response.dto';
import { OrderDto } from '../dto/order.dto';
import { OrderStatus } from '../models/order-status';
import { generateSecureNumericOrderNumber } from '../../shared/utils/order-number-generator';

export const OrderRepositoryPortToken = Symbol('OrderRepositoryPort');
export const OrderManagementServicePortToken = Symbol('OrderManagementServicePort');

export const OrderDtoMockDocumentation: OrderDto = {
  id: `${generateNewObjectId()}`,
  document: '12345678900',
  items: [
    {
      name: 'Misto quente',
      description: 'Misto quente guloso',
      quantity: 1,
      unitPrice: 10,
    },
  ],
  paymentInformation: {
    paymentMethod: PaymentMethod.QRCODEPIX,
    paymentUrl: 'url-de-pagamento.com.br',
    qrCodeUrl: 'qr-code-url-de-pagamento.com.br',
    paymentProof: 'comprovante',
    totalPaid: 10,
    confirmed: true,
    createDate: new Date(),
    updateDate: new Date(),
  },
  total: 10,
  orderNumber: generateSecureNumericOrderNumber(),
} as OrderDto;

export const OrderResponseDtoMockDocumentation: OrderResponseDto = {
  id: `${generateNewObjectId()}`,
  orderNumber: 123,
  orderStatus: OrderStatus.COMPLETED,
  receivedDate: new Date(),
  lastUpdateDate: new Date(),
};
