import { Controller, Get, Put, Param } from '@nestjs/common';
import { ApiTags, ApiResponse, ApiParam } from '@nestjs/swagger';
import { OrderStatus } from '../models/order-status';
import { KitchenService } from '../control/services/kitchen.service';
import { generateNewObjectId } from 'src/modules/shared/database/helpers/generate-objectId';
import { OrderResponseDtoMockDocumentation } from '../constants/order.constants';
import { OrderDto } from '../dto/order.dto';

@ApiTags('Cozinha')
@Controller('kitchen/orders')
export class KitchenController {
  constructor(private readonly kitchenService: KitchenService) {}

  @Get()
  @ApiResponse({
    status: 200,
    description: 'Lista de pedidos encontrada com sucesso',
    schema: {
      example: {
        success: true,
        return: [OrderResponseDtoMockDocumentation],
      },
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Nenhum pedido listado',
    schema: {
      example: {
        success: false,
        return: [],
      },
    },
  })
  async findOrdersStatus(): Promise<Array<OrderDto>> {
    return await this.kitchenService.findOrdersStatus();  
  }

  @Put(':orderId/status/:status')
  @ApiParam({
    name: 'orderId',
    description: 'Id do pedido',
    example: generateNewObjectId(),
  })
  @ApiParam({
    name: 'status',
    description: 'Status do pedido',
    example: OrderStatus.READY,
    enum: OrderStatus,
  })
  @ApiResponse({ status: 200, description: 'Atualiza status do pedido', schema: { example: { success: true, return: true } } })
  @ApiResponse({
    status: 500,
    description: 'Id do pedido não informado',
    content: {
      'aaplication/json': {
        examples: {
          id_invalido: {
            summary: 'Id do pedido não informado',
            value: { success: false, return: { errorCode: 'ORDERID_IS_REQUIRED', message: 'Id do pedido inválido' } },
          },
          status_invalido: {
            summary: 'Status do pedido não informado',
            value: { success: false, return: { errorCode: 'STATUS_IS_REQUIRED', message: 'Status do pedido inválido' } },
          },
        },
      },
    },
  })
  async changeOrderStatus(@Param('orderId') orderId: string, @Param('status') status: OrderStatus): Promise<boolean> {
    return await this.kitchenService.changeOrderStatus(orderId, status);
  }
}
