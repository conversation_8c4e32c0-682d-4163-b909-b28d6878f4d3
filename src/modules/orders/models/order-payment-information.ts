import { VitolaException } from "../../shared/api/exceptions/models/vitola-exception-builder.model";
import { PaymentMethod } from "../dto/order-payment-method";

export class PaymentInformation {
    paymentMethod: PaymentMethod;

    paymentUrl: string;

    qrCodeUrl: string;

    paymentProof: string;

    totalPaid: number;

    confirmed: boolean;

    createDate: Date;
    updateDate: Date;

    constructor(qrCodeUrl: string, paymentUrl: string) {
        this.qrCodeUrl = qrCodeUrl;
        this.paymentUrl = paymentUrl;
        this.createDate = new Date();
        this.paymentMethod = PaymentMethod.QRCODEPIX;
        this.confirmed = false;
    }

    static validate(paymentInfo: PaymentInformation) {
        if (!paymentInfo.paymentMethod) {
            throw VitolaException.ofValidation("PAYMENT_METHOD_NOT_FOUND", "Forma de pagamento é obrigatória.")
        }

        if (paymentInfo.paymentMethod == PaymentMethod.QRCODEPIX && !paymentInfo.qrCodeUrl) {
            throw VitolaException.ofValidation("QRCODE_NOT_FOUND", "QRCode do pagamento é obrigatório.")
        }

        if (!paymentInfo.paymentProof) {
            throw VitolaException.ofValidation("PAYMENT_PROOF_NOT_FOUND", "Comprovante de pagamento é obrigatória.")
        }
        
        if (!paymentInfo.totalPaid) {
            throw VitolaException.ofValidation("TOTAL_PAID_NOT_FOUND", "Valor do pagamento é obrigatória.")
        }

    }
}