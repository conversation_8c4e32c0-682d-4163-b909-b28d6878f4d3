import { ArgumentsHost, Catch, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { BaseExceptionFilter } from '@nestjs/core';
import { Response } from 'express';
import * as Joi from 'joi';
import { VitolaException } from '../exceptions/models/vitola-exception-builder.model';
import { ApiReturn } from '../models/api-return.model';

@Catch()
export class AnyExceptionFilter extends BaseExceptionFilter {
  constructor() {
    super();
  }

  catch(exception: HttpException, host: ArgumentsHost): void {
    const response = this.getHostResponse(host);
    const exceptionMessage = this.getExceptionMessage(exception);
    const status = this.getStatus(exception);

    Logger.error(exceptionMessage, exception.stack);

    const apiReturn = this.buildApiReturnObject(exception, exceptionMessage);

    response.status(status).json(apiReturn);
  }

  private getHostResponse(host: ArgumentsHost): Response {
    return host.switchToHttp().getResponse<Response>();
  }

  private getStatus(exception: HttpException): HttpStatus {
    try {
      if (this.isJoiValidationErrorType(exception)) {
        return HttpStatus.BAD_REQUEST;
      }

      return exception.getStatus();
    } catch (error) {
      console.log(error);
      return HttpStatus.INTERNAL_SERVER_ERROR;
    }
  }

  private isJoiValidationErrorType(exception: HttpException): boolean {
    return exception instanceof Joi.ValidationError;
  }

  private getErrorCode(exception: HttpException): string {
    if (this.isJoiValidationErrorType(exception)) {
      return 'VALIDATION_ERROR';
    }
    if (exception instanceof VitolaException || exception?.name === 'VitolaException') {
      return (exception as VitolaException).getErrorCode();
    }
    return 'UNEXPECTED_ERROR';
  }

  private getExceptionMessage(exception: HttpException | any): string {
    const exceptionMessage = exception.response.message ?? '';

    if (exceptionMessage && typeof exceptionMessage === 'object' && exceptionMessage.length) {
      return exception.response.message[0];
    } else {
      return exception.message;
    }
  }

  private buildApiReturnObject(exception: HttpException, exceptionMessage: string): ApiReturn<null> {
    return {
      success: false,
      return: null,
      error: exceptionMessage,
      errorCode: this.getErrorCode(exception),
    } as ApiReturn<null>;
  }
}
