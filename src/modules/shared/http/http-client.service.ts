import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { AxiosResponse } from 'axios';
import { Observable, catchError, map, throwError } from 'rxjs';

export interface HttpClientConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

@Injectable()
export class HttpClientService {
  private readonly logger = new Logger(HttpClientService.name);
  private readonly ordersApiUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.ordersApiUrl = process.env.ORDERS_API_URL
    this.logger.log(`Orders API URL configured: ${this.ordersApiUrl}`);
  }

  /**
   * Realiza uma requisição GET
   */
  get<T>(endpoint: string, config?: any): Observable<T> {
    const url = `${this.ordersApiUrl}${endpoint}`;
    this.logger.debug(`GET ${url}`);

    return this.httpService.get<T>(url, config).pipe(
      map((response: AxiosResponse<T>) => {
        console.log(response.data['return'])
        return response.data['return'];
      }),
      catchError((error) => this.handleError('GET', url, error))
    );
  }

  /**
   * Realiza uma requisição POST
   */
  post<T>(endpoint: string, data?: any, config?: any): Observable<T> {
    const url = `${this.ordersApiUrl}${endpoint}`;
    this.logger.debug(`POST ${url}`, data);

    return this.httpService.post<T>(url, data, config).pipe(
      map((response: AxiosResponse<T>) => response.data['return']),
      catchError((error) => this.handleError('POST', url, error))
    );
  }

  /**
   * Realiza uma requisição PUT
   */
  put<T>(endpoint: string, data?: any, config?: any): Observable<T> {
    const url = `${this.ordersApiUrl}${endpoint}`;
    this.logger.debug(`PUT ${url}`, data);

    return this.httpService.put<T>(url, data, config).pipe(
      map((response: AxiosResponse<T>) => response.data['return']),
      catchError((error) => this.handleError('PUT', url, error))
    );
  }

  /**
   * Realiza uma requisição PATCH
   */
  patch<T>(endpoint: string, data?: any, config?: any): Observable<T> {
    const url = `${this.ordersApiUrl}${endpoint}`;
    this.logger.debug(`PATCH ${url}`, data);

    return this.httpService.patch<T>(url, data, config).pipe(
      map((response: AxiosResponse<T>) => response.data['return']),
      catchError((error) => this.handleError('PATCH', url, error))
    );
  }

  /**
   * Realiza uma requisição DELETE
   */
  delete<T>(endpoint: string, config?: any): Observable<T> {
    const url = `${this.ordersApiUrl}${endpoint}`;
    this.logger.debug(`DELETE ${url}`);

    return this.httpService.delete<T>(url, config).pipe(
      map((response: AxiosResponse<T>) => response.data['return']),
      catchError((error) => this.handleError('DELETE', url, error))
    );
  }

  /**
   * Trata erros das requisições HTTP
   */
  private handleError(method: string, url: string, error: any): Observable<never> {
    const errorMessage = error.response?.data?.message || error.message || 'Unknown error';
    const statusCode = error.response?.status || 500;
    
    this.logger.error(`${method} ${url} failed:`, {
      status: statusCode,
      message: errorMessage,
      data: error.response?.data
    });

    return throwError(() => ({
      statusCode,
      message: errorMessage,
      method,
      url,
      timestamp: new Date().toISOString()
    }));
  }

  /**
   * Verifica se a API de pedidos está disponível
   */
  async healthCheck(): Promise<boolean> {
    try {
      const response = await this.httpService.get(`${this.ordersApiUrl}/health`).toPromise();
      return response?.status === 200;
    } catch (error) {
      this.logger.warn('Orders API health check failed:', error.message);
      return false;
    }
  }

  /**
   * Obtém a URL base configurada
   */
  getBaseUrl(): string {
    return this.ordersApiUrl;
  }
}
