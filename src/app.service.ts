import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getApiInfo(): object {
    return {
      name: 'API Kitchen - Vitola Lanches',
      version: '1.0.0',
      description: 'API para gerenciamento da cozinha do Vitola Lanches',
      endpoints: {
        orders: '/kitchen/orders',
        docs: '/api/docs',
        health: '/health'
      },
      features: [
        'Listagem de pedidos da cozinha',
        'Alteração de status dos pedidos',
        'Documentação interativa com Swagger'
      ]
    };
  }

  getHealth(): object {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      version: process.version
    };
  }
}
