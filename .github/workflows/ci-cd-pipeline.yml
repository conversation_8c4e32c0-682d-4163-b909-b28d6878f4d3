name: Deploy Api Kitchen

on:
  push:
    branches:
      - main
      
jobs:
  CI-CD:
    uses: vitola-lanches/actions/.github/workflows/deploy-nodejs.yml@main
    with:
      service_name: api-kitchen
      dockerfile_path: dockerfiles/Dockerfile-node
    secrets:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
      SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: ${{ secrets.AWS_REGION }}
      AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}
      CLUSTER_NAME: ${{ secrets.CLUSTER_NAME }}
      DATABASE_URL: ${{ secrets.DATABASE_URL }}
      ORDERS_API_URL: ${{ secrets.ORDERS_API_URL }}