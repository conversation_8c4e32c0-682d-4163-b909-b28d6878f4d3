# Configurações do Projeto
sonar.projectKey=vitola-lanches_api-kitchen
sonar.organization=vitola-lanches
sonar.projectName=api-kitchen
sonar.projectVersion=1.0
# Configurações para SonarCloud (usado no CI/CD)
# sonar.host.url será automaticamente definido como https://sonarcloud.io pelo GitHub Action

# Configurações de Código Fonte
sonar.sources=src
sonar.tests=test
sonar.test.inclusions=test/**/*.spec.ts,test/**/*.e2e-spec.ts

# Exclusões
sonar.exclusions=**/node_modules/**,**/dist/**,**/coverage/**,**/build/**,**/*.d.ts
sonar.test.exclusions=**/node_modules/**,**/dist/**,**/coverage/**

# Configurações de Cobertura
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.typescript.lcov.reportPaths=coverage/lcov.info
sonar.testExecutionReportPaths=test-report.xml
sonar.coverage.exclusions=**/*.spec.ts,**/*.test.ts,**/*.e2e-spec.ts,**/main.ts,**/test/**,**/*.module.ts,**/*.interface.ts,**/*.enum.ts,**/index.ts,**/*.filter.ts,**/*.interceptor.ts,**/*.constants.ts,**/models/**,**/dto/**

# Configurações de Duplicação
sonar.cpd.exclusions=**/*.spec.ts,**/*.e2e-spec.ts,**/test/**

# Configurações específicas para TypeScript
sonar.typescript.tsconfigPath=tsconfig.json
sonar.sourceEncoding=UTF-8
sonar.language=ts

# Configurações de Qualidade
sonar.qualitygate.wait=true

# Configurações de Issues (para ignorar algumas regras específicas)
sonar.issue.ignore.multicriteria=e1,e2,e3

# Rule: "Tests should include assertions"
sonar.issue.ignore.multicriteria.e1.ruleKey=typescript:S2699
sonar.issue.ignore.multicriteria.e1.resourceKey=**/*.spec.ts

# Rule: "Unused function parameters should be removed" (comum em NestJS)
sonar.issue.ignore.multicriteria.e2.ruleKey=typescript:S1172
sonar.issue.ignore.multicriteria.e2.resourceKey=**/*.ts

# Rule: "Functions should not have too many parameters" (comum em construtores)
sonar.issue.ignore.multicriteria.e3.ruleKey=typescript:S107
sonar.issue.ignore.multicriteria.e3.resourceKey=**/*.ts

# Configurações adicionais para TypeScript/NestJS
sonar.javascript.environments=node
sonar.typescript.node=node_modules/typescript/lib/typescript.js