#!/usr/bin/env node

// Simple script to test module resolution
// Run with: node test-modules.js

console.log('🔍 Testing Module Resolution...\n');

const modules = [
  '@nestjs/axios',
  '@nestjs/common',
  '@nestjs/config',
  '@nestjs/core',
  'mongoose',
  'axios',
  'rxjs'
];

modules.forEach(moduleName => {
  try {
    const resolved = require.resolve(moduleName);
    console.log(`✅ ${moduleName} - ${resolved}`);
  } catch (error) {
    console.log(`❌ ${moduleName} - ${error.message}`);
  }
});

console.log('\n🔍 Testing specific imports...\n');

// Test specific imports that are causing issues
try {
  const { HttpService } = require('@nestjs/axios');
  console.log('✅ HttpService from @nestjs/axios - OK');
} catch (error) {
  console.log(`❌ HttpService from @nestjs/axios - ${error.message}`);
}

try {
  const { Types } = require('mongoose');
  console.log('✅ Types from mongoose - OK');
} catch (error) {
  console.log(`❌ Types from mongoose - ${error.message}`);
}

try {
  const { Injectable } = require('@nestjs/common');
  console.log('✅ Injectable from @nestjs/common - OK');
} catch (error) {
  console.log(`❌ Injectable from @nestjs/common - ${error.message}`);
}

console.log('\n🏁 Module resolution test completed');
