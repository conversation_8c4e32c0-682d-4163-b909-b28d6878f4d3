<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order-item.dto.spec.ts">
    <testCase name="OrderItemDto Properties should have all required properties" duration="54"/>
    <testCase name="OrderItemDto Properties should accept valid values" duration="1"/>
    <testCase name="OrderItemDto validate should validate successfully with valid data" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when name is missing" duration="164"/>
    <testCase name="OrderItemDto validate should throw error when name is null" duration="5"/>
    <testCase name="OrderItemDto validate should throw error when description is missing" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when description is null" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when quantity is missing" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when quantity is zero" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when quantity is negative" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is missing" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is zero" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is negative" duration="6"/>
    <testCase name="OrderItemDto validate should validate multiple valid items" duration="1"/>
    <testCase name="OrderItemDto validate should handle edge cases for valid values" duration="1"/>
    <testCase name="OrderItemDto validate should handle large values" duration="6"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/app.controller.spec.ts">
    <testCase name="AppController getApiInfo should return API information" duration="182"/>
    <testCase name="AppController getApiInfo should call appService.getApiInfo" duration="15"/>
    <testCase name="AppController getApiInfo should return object with required properties" duration="9"/>
    <testCase name="AppController getHealth should return health check information" duration="4"/>
    <testCase name="AppController getHealth should call appService.getHealth" duration="5"/>
    <testCase name="AppController getHealth should return object with health properties" duration="7"/>
    <testCase name="AppController getHealth should return status ok" duration="13"/>
    <testCase name="AppController Controller Integration should be defined" duration="3"/>
    <testCase name="AppController Controller Integration should have appService injected" duration="5"/>
    <testCase name="AppController Controller Integration should handle multiple concurrent requests" duration="7"/>
    <testCase name="AppController Controller Integration should maintain consistent responses" duration="5"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/shared/api/exceptions/models/vitola-exception-builder.model.spec.ts">
    <testCase name="VitolaException constructor should create instance with default message" duration="1"/>
    <testCase name="VitolaException constructor should create instance with custom message" duration="0"/>
    <testCase name="VitolaException static factory methods ofValidation should create validation exception with required parameters" duration="1"/>
    <testCase name="VitolaException static factory methods ofValidation should create validation exception with all parameters" duration="1"/>
    <testCase name="VitolaException static factory methods ofError should create internal server error exception" duration="0"/>
    <testCase name="VitolaException static factory methods ofError should create error exception with context and trace" duration="1"/>
    <testCase name="VitolaException static factory methods ofNotFound should create not found exception" duration="0"/>
    <testCase name="VitolaException static factory methods ofNotFound should create not found exception with all parameters" duration="1"/>
    <testCase name="VitolaException builder methods should set error code" duration="0"/>
    <testCase name="VitolaException builder methods should set message" duration="2"/>
    <testCase name="VitolaException builder methods should set HTTP status" duration="1"/>
    <testCase name="VitolaException builder methods should set context name" duration="0"/>
    <testCase name="VitolaException builder methods should set trace ID" duration="1"/>
    <testCase name="VitolaException builder methods should set response detail" duration="0"/>
    <testCase name="VitolaException builder methods should set data" duration="0"/>
    <testCase name="VitolaException method chaining should allow method chaining" duration="1"/>
    <testCase name="VitolaException inheritance should extend HttpException" duration="1"/>
    <testCase name="VitolaException inheritance should maintain prototype chain" duration="0"/>
    <testCase name="VitolaException error scenarios should handle undefined parameters gracefully" duration="0"/>
    <testCase name="VitolaException error scenarios should handle null parameters gracefully" duration="1"/>
    <testCase name="VitolaException error scenarios should handle empty string parameters" duration="0"/>
    <testCase name="VitolaException complex scenarios should create complex validation exception" duration="0"/>
    <testCase name="VitolaException complex scenarios should create complex error with full context" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/shared/utils/order-number-generator.spec.ts">
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate order number with correct format" duration="2"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate different numbers on consecutive calls" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate unique order numbers with high probability" duration="5"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should include current date in order number" duration="2"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should extract correct date from order number" duration="3"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate positive numeric order number" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate different numbers on consecutive calls" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate unique numeric order numbers with reasonable collision rate" duration="15"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate numbers within reasonable range" duration="3"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate numbers with expected structure" duration="14"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should be cryptographically secure (statistical test)" duration="27"/>
    <testCase name="OrderNumberGenerator isValidOrderNumberFormat should validate correct format" duration="0"/>
    <testCase name="OrderNumberGenerator isValidOrderNumberFormat should reject invalid formats" duration="1"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should extract correct date from valid order number" duration="1"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should return null for invalid format" duration="0"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should handle edge dates correctly" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/app.service.spec.ts">
    <testCase name="AppService getApiInfo should be defined" duration="10"/>
    <testCase name="AppService getApiInfo should return API information object" duration="6"/>
    <testCase name="AppService getApiInfo should return correct API name" duration="3"/>
    <testCase name="AppService getApiInfo should return correct API version" duration="7"/>
    <testCase name="AppService getApiInfo should return correct API description" duration="22"/>
    <testCase name="AppService getApiInfo should return all required properties" duration="10"/>
    <testCase name="AppService getApiInfo should return correct endpoints" duration="3"/>
    <testCase name="AppService getApiInfo should return correct features array" duration="4"/>
    <testCase name="AppService getApiInfo should return features as array" duration="6"/>
    <testCase name="AppService getApiInfo should return consistent data on multiple calls" duration="20"/>
    <testCase name="AppService getApiInfo should return immutable object structure" duration="8"/>
    <testCase name="AppService getHealth should be defined" duration="1"/>
    <testCase name="AppService getHealth should return health check object" duration="3"/>
    <testCase name="AppService getHealth should return all required health properties" duration="8"/>
    <testCase name="AppService getHealth should return status as ok" duration="24"/>
    <testCase name="AppService getHealth should return valid timestamp" duration="24"/>
    <testCase name="AppService getHealth should return current timestamp" duration="2"/>
    <testCase name="AppService getHealth should return valid uptime" duration="3"/>
    <testCase name="AppService getHealth should return memory usage object" duration="25"/>
    <testCase name="AppService getHealth should return valid memory values" duration="14"/>
    <testCase name="AppService getHealth should return Node.js version" duration="7"/>
    <testCase name="AppService getHealth should return different timestamps on multiple calls" duration="25"/>
    <testCase name="AppService getHealth should return increasing uptime on multiple calls" duration="15"/>
    <testCase name="AppService Service Integration should be defined" duration="2"/>
    <testCase name="AppService Service Integration should be an instance of AppService" duration="4"/>
    <testCase name="AppService Service Integration should handle concurrent calls correctly" duration="3"/>
    <testCase name="AppService Service Integration should maintain performance under load" duration="6"/>
    <testCase name="AppService Service Integration should not throw errors on repeated calls" duration="7"/>
    <testCase name="AppService Edge Cases should handle getApiInfo without errors" duration="2"/>
    <testCase name="AppService Edge Cases should handle getHealth without errors" duration="4"/>
    <testCase name="AppService Edge Cases should return valid JSON serializable objects" duration="9"/>
    <testCase name="AppService Edge Cases should return objects that can be safely cloned" duration="15"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/shared/http/http-client.service.spec.ts">
    <testCase name="HttpClientService constructor should be defined" duration="38"/>
    <testCase name="HttpClientService constructor should configure orders API URL from environment" duration="4"/>
    <testCase name="HttpClientService get should make GET request and return data" duration="9"/>
    <testCase name="HttpClientService get should handle GET request with config" duration="5"/>
    <testCase name="HttpClientService get should handle GET request errors" duration="16"/>
    <testCase name="HttpClientService post should make POST request and return data" duration="9"/>
    <testCase name="HttpClientService post should handle POST request with config" duration="10"/>
    <testCase name="HttpClientService post should handle POST request errors" duration="7"/>
    <testCase name="HttpClientService put should make PUT request and return data" duration="4"/>
    <testCase name="HttpClientService put should handle PUT request with config" duration="4"/>
    <testCase name="HttpClientService put should handle PUT request errors" duration="5"/>
    <testCase name="HttpClientService patch should make PATCH request and return data" duration="5"/>
    <testCase name="HttpClientService delete should make DELETE request and return data" duration="8"/>
    <testCase name="HttpClientService healthCheck should perform health check successfully" duration="2"/>
    <testCase name="HttpClientService healthCheck should handle health check failure" duration="3"/>
    <testCase name="HttpClientService getBaseUrl should return configured base URL" duration="3"/>
    <testCase name="HttpClientService error handling should handle network errors" duration="3"/>
    <testCase name="HttpClientService error handling should handle HTTP errors with status codes" duration="7"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order-response.dto.spec.ts">
    <testCase name="OrderResponseDto constructor should create OrderResponseDto with all properties" duration="24"/>
    <testCase name="OrderResponseDto constructor should create OrderResponseDto with different order statuses" duration="1"/>
    <testCase name="OrderResponseDto constructor should handle different order numbers" duration="2"/>
    <testCase name="OrderResponseDto constructor should handle different date values" duration="2"/>
    <testCase name="OrderResponseDto of static method should create OrderResponseDto from Order entity" duration="2"/>
    <testCase name="OrderResponseDto of static method should handle Order with all different statuses" duration="1"/>
    <testCase name="OrderResponseDto of static method should handle Order with different order numbers" duration="2"/>
    <testCase name="OrderResponseDto of static method should preserve date information from Order" duration="1"/>
    <testCase name="OrderResponseDto of static method should handle Order with complex data" duration="1"/>
    <testCase name="OrderResponseDto of static method should create multiple DTOs from multiple Orders" duration="11"/>
    <testCase name="OrderResponseDto of static method should handle Orders with different statuses in batch" duration="2"/>
    <testCase name="OrderResponseDto DTO Properties should have all required properties defined" duration="4"/>
    <testCase name="OrderResponseDto DTO Properties should have correct property types" duration="1"/>
    <testCase name="OrderResponseDto DTO Properties should handle edge cases for order numbers" duration="0"/>
    <testCase name="OrderResponseDto DTO Properties should handle empty string id" duration="1"/>
    <testCase name="OrderResponseDto DTO Properties should maintain immutability of date objects" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order.dto.spec.ts">
    <testCase name="OrderDto Properties should have all required properties" duration="14"/>
    <testCase name="OrderDto validate should validate successfully with valid data" duration="3"/>
    <testCase name="OrderDto validate should throw error when items are missing" duration="100"/>
    <testCase name="OrderDto validate should throw error when items array is null" duration="34"/>
    <testCase name="OrderDto validate should throw error when order is null" duration="7"/>
    <testCase name="OrderDto validate should validate all items in the order" duration="3"/>
    <testCase name="OrderDto validate should generate unique id for each order" duration="3"/>
    <testCase name="OrderDto validate should set status to RECEIVED" duration="0"/>
    <testCase name="OrderDto validate should generate secure order number" duration="1"/>
    <testCase name="OrderDto validate should generate unique order numbers" duration="1"/>
    <testCase name="OrderDto validatePayment should validate successfully with valid payment" duration="1"/>
    <testCase name="OrderDto validatePayment should throw error when payment information is missing" duration="1"/>
    <testCase name="OrderDto validatePayment should throw error when payment information is undefined" duration="2"/>
    <testCase name="OrderDto calculateTotal should calculate total correctly for single item" duration="0"/>
    <testCase name="OrderDto calculateTotal should calculate total correctly for multiple items" duration="0"/>
    <testCase name="OrderDto calculateTotal should return zero for empty items array" duration="0"/>
    <testCase name="OrderDto calculateTotal should handle decimal values correctly" duration="0"/>
    <testCase name="OrderDto Integration Tests should validate and calculate total in sequence" duration="1"/>
    <testCase name="OrderDto Integration Tests should handle complex order with multiple items" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/controllers/kitchen.controller.spec.ts">
    <testCase name="KitchenController findOrdersStatus should return list of orders successfully" duration="90"/>
    <testCase name="KitchenController findOrdersStatus should return empty array when no orders found" duration="4"/>
    <testCase name="KitchenController findOrdersStatus should handle service errors when finding orders" duration="28"/>
    <testCase name="KitchenController findOrdersStatus should return orders with different statuses" duration="15"/>
    <testCase name="KitchenController changeOrderStatus should change order status successfully" duration="5"/>
    <testCase name="KitchenController changeOrderStatus should handle invalid order id" duration="15"/>
    <testCase name="KitchenController changeOrderStatus should handle invalid status" duration="13"/>
    <testCase name="KitchenController changeOrderStatus should handle order not found" duration="5"/>
    <testCase name="KitchenController changeOrderStatus should handle service errors when changing status" duration="8"/>
    <testCase name="KitchenController changeOrderStatus should change status to all valid statuses" duration="24"/>
    <testCase name="KitchenController Controller Integration should be defined" duration="15"/>
    <testCase name="KitchenController Controller Integration should have kitchenService injected" duration="7"/>
    <testCase name="KitchenController Controller Integration should handle concurrent requests" duration="3"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/control/services/kitchen.service.spec.ts">
    <testCase name="KitchenService constructor should be defined" duration="28"/>
    <testCase name="KitchenService constructor should have orderRepository injected" duration="3"/>
    <testCase name="KitchenService findOrdersStatus should return orders when repository returns data" duration="5"/>
    <testCase name="KitchenService findOrdersStatus should return empty array when no orders found" duration="2"/>
    <testCase name="KitchenService findOrdersStatus should handle repository errors" duration="157"/>
    <testCase name="KitchenService changeOrderStatus should change order status successfully" duration="4"/>
    <testCase name="KitchenService changeOrderStatus should handle invalid order ID" duration="16"/>
    <testCase name="KitchenService changeOrderStatus should handle invalid status" duration="16"/>
    <testCase name="KitchenService changeOrderStatus should handle repository errors" duration="11"/>
    <testCase name="KitchenService changeOrderStatus should handle different order statuses" duration="21"/>
    <testCase name="KitchenService findOrderById should find order by ID successfully" duration="5"/>
    <testCase name="KitchenService findOrderById should handle order not found" duration="4"/>
    <testCase name="KitchenService findOrderById should handle invalid order ID" duration="13"/>
    <testCase name="KitchenService findOrderById should handle repository errors" duration="5"/>
    <testCase name="KitchenService findOrdersByStatus should find orders by status successfully" duration="2"/>
    <testCase name="KitchenService findOrdersByStatus should return empty array when no orders found for status" duration="2"/>
    <testCase name="KitchenService findOrdersByStatus should handle invalid status" duration="4"/>
    <testCase name="KitchenService findOrdersByStatus should handle repository errors" duration="4"/>
    <testCase name="KitchenService error handling should handle multiple concurrent requests" duration="20"/>
    <testCase name="KitchenService error handling should handle service unavailable scenarios" duration="3"/>
    <testCase name="KitchenService integration scenarios should handle complete order workflow" duration="2"/>
  </file>
</testExecutions>