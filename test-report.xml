<?xml version="1.0" encoding="UTF-8"?>
<testExecutions version="1">
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/shared/utils/order-number-generator.spec.ts">
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate order number with correct format" duration="10"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate different numbers on consecutive calls" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should generate unique order numbers with high probability" duration="2"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should include current date in order number" duration="0"/>
    <testCase name="OrderNumberGenerator generateSecureOrderNumber should extract correct date from order number" duration="0"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate positive numeric order number" duration="1"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate different numbers on consecutive calls" duration="0"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate unique numeric order numbers with reasonable collision rate" duration="119"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate numbers within reasonable range" duration="64"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should generate numbers with expected structure" duration="4"/>
    <testCase name="OrderNumberGenerator generateSecureNumericOrderNumber should be cryptographically secure (statistical test)" duration="15"/>
    <testCase name="OrderNumberGenerator isValidOrderNumberFormat should validate correct format" duration="0"/>
    <testCase name="OrderNumberGenerator isValidOrderNumberFormat should reject invalid formats" duration="1"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should extract correct date from valid order number" duration="0"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should return null for invalid format" duration="0"/>
    <testCase name="OrderNumberGenerator extractDateFromOrderNumber should handle edge dates correctly" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/app.service.spec.ts">
    <testCase name="AppService getApiInfo should be defined" duration="109"/>
    <testCase name="AppService getApiInfo should return API information object" duration="14"/>
    <testCase name="AppService getApiInfo should return correct API name" duration="14"/>
    <testCase name="AppService getApiInfo should return correct API version" duration="6"/>
    <testCase name="AppService getApiInfo should return correct API description" duration="4"/>
    <testCase name="AppService getApiInfo should return all required properties" duration="3"/>
    <testCase name="AppService getApiInfo should return correct endpoints" duration="3"/>
    <testCase name="AppService getApiInfo should return correct features array" duration="2"/>
    <testCase name="AppService getApiInfo should return features as array" duration="5"/>
    <testCase name="AppService getApiInfo should return consistent data on multiple calls" duration="3"/>
    <testCase name="AppService getApiInfo should return immutable object structure" duration="10"/>
    <testCase name="AppService getHealth should be defined" duration="2"/>
    <testCase name="AppService getHealth should return health check object" duration="2"/>
    <testCase name="AppService getHealth should return all required health properties" duration="86"/>
    <testCase name="AppService getHealth should return status as ok" duration="2"/>
    <testCase name="AppService getHealth should return valid timestamp" duration="2"/>
    <testCase name="AppService getHealth should return current timestamp" duration="2"/>
    <testCase name="AppService getHealth should return valid uptime" duration="2"/>
    <testCase name="AppService getHealth should return memory usage object" duration="3"/>
    <testCase name="AppService getHealth should return valid memory values" duration="3"/>
    <testCase name="AppService getHealth should return Node.js version" duration="1"/>
    <testCase name="AppService getHealth should return different timestamps on multiple calls" duration="13"/>
    <testCase name="AppService getHealth should return increasing uptime on multiple calls" duration="14"/>
    <testCase name="AppService Service Integration should be defined" duration="1"/>
    <testCase name="AppService Service Integration should be an instance of AppService" duration="2"/>
    <testCase name="AppService Service Integration should handle concurrent calls correctly" duration="13"/>
    <testCase name="AppService Service Integration should maintain performance under load" duration="2"/>
    <testCase name="AppService Service Integration should not throw errors on repeated calls" duration="9"/>
    <testCase name="AppService Edge Cases should handle getApiInfo without errors" duration="9"/>
    <testCase name="AppService Edge Cases should handle getHealth without errors" duration="2"/>
    <testCase name="AppService Edge Cases should return valid JSON serializable objects" duration="2"/>
    <testCase name="AppService Edge Cases should return objects that can be safely cloned" duration="36"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order-item.dto.spec.ts">
    <testCase name="OrderItemDto Properties should have all required properties" duration="8"/>
    <testCase name="OrderItemDto Properties should accept valid values" duration="0"/>
    <testCase name="OrderItemDto validate should validate successfully with valid data" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when name is missing" duration="132"/>
    <testCase name="OrderItemDto validate should throw error when name is null" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when description is missing" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when description is null" duration="4"/>
    <testCase name="OrderItemDto validate should throw error when quantity is missing" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when quantity is zero" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when quantity is negative" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is missing" duration="2"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is zero" duration="1"/>
    <testCase name="OrderItemDto validate should throw error when unitPrice is negative" duration="0"/>
    <testCase name="OrderItemDto validate should validate multiple valid items" duration="1"/>
    <testCase name="OrderItemDto validate should handle edge cases for valid values" duration="1"/>
    <testCase name="OrderItemDto validate should handle large values" duration="0"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/app.controller.spec.ts">
    <testCase name="AppController getApiInfo should return API information" duration="29"/>
    <testCase name="AppController getApiInfo should call appService.getApiInfo" duration="3"/>
    <testCase name="AppController getApiInfo should return object with required properties" duration="5"/>
    <testCase name="AppController getHealth should return health check information" duration="4"/>
    <testCase name="AppController getHealth should call appService.getHealth" duration="3"/>
    <testCase name="AppController getHealth should return object with health properties" duration="3"/>
    <testCase name="AppController getHealth should return status ok" duration="5"/>
    <testCase name="AppController Controller Integration should be defined" duration="2"/>
    <testCase name="AppController Controller Integration should have appService injected" duration="4"/>
    <testCase name="AppController Controller Integration should handle multiple concurrent requests" duration="5"/>
    <testCase name="AppController Controller Integration should maintain consistent responses" duration="6"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order.dto.spec.ts">
    <testCase name="OrderDto Properties should have all required properties" duration="8"/>
    <testCase name="OrderDto validate should validate successfully with valid data" duration="3"/>
    <testCase name="OrderDto validate should throw error when items are missing" duration="20"/>
    <testCase name="OrderDto validate should throw error when items array is null" duration="1"/>
    <testCase name="OrderDto validate should throw error when order is null" duration="1"/>
    <testCase name="OrderDto validate should validate all items in the order" duration="1"/>
    <testCase name="OrderDto validate should generate unique id for each order" duration="1"/>
    <testCase name="OrderDto validate should set status to RECEIVED" duration="1"/>
    <testCase name="OrderDto validate should generate secure order number" duration="1"/>
    <testCase name="OrderDto validate should generate unique order numbers" duration="0"/>
    <testCase name="OrderDto validatePayment should validate successfully with valid payment" duration="1"/>
    <testCase name="OrderDto validatePayment should throw error when payment information is missing" duration="1"/>
    <testCase name="OrderDto validatePayment should throw error when payment information is undefined" duration="1"/>
    <testCase name="OrderDto calculateTotal should calculate total correctly for single item" duration="0"/>
    <testCase name="OrderDto calculateTotal should calculate total correctly for multiple items" duration="0"/>
    <testCase name="OrderDto calculateTotal should return zero for empty items array" duration="0"/>
    <testCase name="OrderDto calculateTotal should handle decimal values correctly" duration="1"/>
    <testCase name="OrderDto Integration Tests should validate and calculate total in sequence" duration="1"/>
    <testCase name="OrderDto Integration Tests should handle complex order with multiple items" duration="1"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/dto/order-response.dto.spec.ts">
    <testCase name="OrderResponseDto constructor should create OrderResponseDto with all properties" duration="8"/>
    <testCase name="OrderResponseDto constructor should create OrderResponseDto with different order statuses" duration="1"/>
    <testCase name="OrderResponseDto constructor should handle different order numbers" duration="4"/>
    <testCase name="OrderResponseDto constructor should handle different date values" duration="1"/>
    <testCase name="OrderResponseDto of static method should create OrderResponseDto from Order entity" duration="2"/>
    <testCase name="OrderResponseDto of static method should handle Order with all different statuses" duration="2"/>
    <testCase name="OrderResponseDto of static method should handle Order with different order numbers" duration="2"/>
    <testCase name="OrderResponseDto of static method should preserve date information from Order" duration="0"/>
    <testCase name="OrderResponseDto of static method should handle Order with complex data" duration="1"/>
    <testCase name="OrderResponseDto of static method should create multiple DTOs from multiple Orders" duration="2"/>
    <testCase name="OrderResponseDto of static method should handle Orders with different statuses in batch" duration="1"/>
    <testCase name="OrderResponseDto DTO Properties should have all required properties defined" duration="1"/>
    <testCase name="OrderResponseDto DTO Properties should have correct property types" duration="1"/>
    <testCase name="OrderResponseDto DTO Properties should handle edge cases for order numbers" duration="0"/>
    <testCase name="OrderResponseDto DTO Properties should handle empty string id" duration="1"/>
    <testCase name="OrderResponseDto DTO Properties should maintain immutability of date objects" duration="0"/>
  </file>
  <file path="/Users/<USER>/Projetos/Vitola-lanches/api-kitchen/test/src/modules/orders/controllers/kitchen.controller.spec.ts">
    <testCase name="KitchenController findOrdersStatus should return list of orders successfully" duration="20"/>
    <testCase name="KitchenController findOrdersStatus should return empty array when no orders found" duration="5"/>
    <testCase name="KitchenController findOrdersStatus should handle service errors when finding orders" duration="15"/>
    <testCase name="KitchenController findOrdersStatus should return orders with different statuses" duration="3"/>
    <testCase name="KitchenController changeOrderStatus should change order status successfully" duration="3"/>
    <testCase name="KitchenController changeOrderStatus should handle invalid order id" duration="6"/>
    <testCase name="KitchenController changeOrderStatus should handle invalid status" duration="4"/>
    <testCase name="KitchenController changeOrderStatus should handle order not found" duration="2"/>
    <testCase name="KitchenController changeOrderStatus should handle service errors when changing status" duration="3"/>
    <testCase name="KitchenController changeOrderStatus should change status to all valid statuses" duration="4"/>
    <testCase name="KitchenController Controller Integration should be defined" duration="2"/>
    <testCase name="KitchenController Controller Integration should have kitchenService injected" duration="6"/>
    <testCase name="KitchenController Controller Integration should handle concurrent requests" duration="1"/>
  </file>
</testExecutions>