# 🍔 Vitola Lanches - API Kitchen

[![CI/CD Pipeline](https://github.com/vitola-lanches/api-kitchen/workflows/CI/CD%20Pipeline/badge.svg)](https://github.com/vitola-lanches/api-kitchen/actions)
[![Build & Push Docker Image](https://github.com/vitola-lanches/api-kitchen/workflows/Build%20&%20Push%20Docker%20Image/badge.svg)](https://github.com/vitola-lanches/api-kitchen/actions)
[![Quality Gate Status](https://sonarcloud.io/api/project_badges/measure?project=vitola-lanches_api-kitchen&metric=alert_status)](https://sonarcloud.io/summary/new_code?id=vitola-lanches_api-kitchen)
[![Coverage](https://sonarcloud.io/api/project_badges/measure?project=vitola-lanches_api-kitchen&metric=coverage)](https://sonarcloud.io/summary/new_code?id=vitola-lanches_api-kitchen)
[![Security Rating](https://sonarcloud.io/api/project_badges/measure?project=vitola-lanches_api-kitchen&metric=security_rating)](https://sonarcloud.io/summary/new_code?id=vitola-lanches_api-kitchen)
[![Maintainability Rating](https://sonarcloud.io/api/project_badges/measure?project=vitola-lanches_api-kitchen&metric=sqale_rating)](https://sonarcloud.io/summary/new_code?id=vitola-lanches_api-kitchen)
[![Docker Image](https://img.shields.io/badge/docker-ghcr.io%2Fvitola--lanches%2Fapi--kitchen-blue)](https://github.com/vitola-lanches/api-kitchen/pkgs/container/api-kitchen)

API REST desenvolvida em NestJS para gerenciamento da cozinha da lanchonete Vitola Lanches, responsável pelo controle de pedidos, status de preparação e organização do fluxo de trabalho da cozinha.

## 📋 Índice

- [Sobre o Projeto](#sobre-o-projeto)
- [Tecnologias](#tecnologias)
- [Arquitetura](#arquitetura)
- [Pré-requisitos](#pré-requisitos)
- [Instalação](#instalação)
- [Configuração](#configuração)
- [Executando a Aplicação](#executando-a-aplicação)
- [Testes](#testes)
- [API Endpoints](#api-endpoints)
- [Docker](#docker)
- [CI/CD](#cicd)
- [SonarQube](#sonarqube)
- [Dados de Exemplo](#dados-de-exemplo)
- [Qualidade de Código](#qualidade-de-código)
- [Contribuição](#contribuição)

## 🎯 Sobre o Projeto

A API Kitchen da Vitola Lanches é um sistema especializado para o gerenciamento da cozinha, oferecendo:

- **Controle de Pedidos**: Visualização e gerenciamento de pedidos recebidos
- **Status de Preparação**: Controle do fluxo de preparação dos pedidos
- **Organização da Cozinha**: Interface otimizada para o ambiente de produção
- **Integração com Sistema Principal**: Sincronização com o sistema de pedidos

## 🚀 Tecnologias

### Backend

- **[NestJS](https://nestjs.com/)** - Framework Node.js progressivo
- **[TypeScript](https://www.typescriptlang.org/)** - Superset tipado do JavaScript
- **[MongoDB](https://www.mongodb.com/)** - Banco de dados NoSQL
- **[Mongoose](https://mongoosejs.com/)** - ODM para MongoDB

### Documentação

- **[Swagger/OpenAPI](https://swagger.io/)** - Documentação interativa da API

### Testes

- **[Jest](https://jestjs.io/)** - Framework de testes
- **[Supertest](https://github.com/visionmedia/supertest)** - Testes de integração HTTP

### DevOps & Qualidade

- **[Docker](https://www.docker.com/)** - Containerização
- **[ESLint](https://eslint.org/)** - Linter para JavaScript/TypeScript
- **[Prettier](https://prettier.io/)** - Formatador de código

## 🏗️ Arquitetura

O projeto segue os princípios da **Arquitetura Hexagonal** (Ports & Adapters) e **Clean Architecture**:

```
src/
├── modules/
│   ├── orders/              # Módulo de pedidos da cozinha
│   │   ├── controllers/     # Controladores REST
│   │   │   └── kitchen.controller.ts
│   │   ├── control/         # Lógica de negócio
│   │   │   ├── services/    # Serviços de aplicação
│   │   │   │   └── kitchen.service.ts
│   │   │   └── repositories/ # Repositórios
│   │   │       └── order.repository.ts
│   │   ├── models/          # Modelos de domínio
│   │   │   └── order-status.ts
│   │   └── schema/          # Schemas do MongoDB
│   │       ├── order.schema.ts
│   │       └── order-item.schema.ts
│   └── shared/              # Módulos compartilhados
│       ├── api/             # Filtros, interceptors
│       └── database/        # Configuração do banco
│           └── connection/
│               └── mongoose.connection.ts
├── app.module.ts            # Módulo principal
└── main.ts                  # Bootstrap da aplicação
```

### Principais Padrões Utilizados

- **Dependency Injection**: Inversão de dependências com NestJS
- **Repository Pattern**: Abstração da camada de dados
- **Service Layer**: Lógica de negócio isolada
- **Schema Pattern**: Validação e estrutura de dados com Mongoose
- **Exception Handling**: Tratamento centralizado de erros

## 📋 Pré-requisitos

- **Node.js** >= 18.0.0
- **Yarn** >= 1.22.0
- **Docker** >= 20.10.0 (opcional)
- **Docker Compose** >= 2.0.0 (opcional)

## 🔧 Instalação

1. **Clone o repositório**

```bash
git clone <repository-url>
cd api-kitchen
```

2. **Instale as dependências**

```bash
yarn install
```

## ⚙️ Configuração

1. **Crie o arquivo de ambiente**

```bash
cp .env.example .env
```

2. **Configure as variáveis de ambiente**

```env
# Banco de dados MongoDB
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=admin-pwd
MONGO_URI=mongodb://mongodb:27017/lanchonete-db?authSource=admin

# Para desenvolvimento local (sem Docker)
# MONGO_URI=************************************************************************

# Aplicação
PORT=3000
NODE_ENV=development
```

## 🚀 Executando a Aplicação

### Desenvolvimento Local

```bash
# Modo desenvolvimento (com hot reload)
yarn start:dev

# Modo debug
yarn start:debug

# Modo produção
yarn build
yarn start:prod
```

### Com Docker Compose

```bash
# Subir todos os serviços
docker-compose up -d

# Ou usar o script de build
./build_app.sh

# Subir apenas o banco de dados
docker-compose up -d mongodb

# Ver logs da aplicação
docker-compose logs -f api-kitchen
```

A aplicação estará disponível em:

- **API**: http://localhost:3000
- **Documentação Swagger**: http://localhost:3000/api/docs
- **Health Check**: http://localhost:3000/health
- **MongoDB**: localhost:27017

## 🧪 Testes

```bash
# Executar todos os testes
yarn test

# Testes em modo watch
yarn test:watch

# Testes com coverage
yarn test:cov

# Testes end-to-end
yarn test:e2e

# Testes específicos
yarn test -- orders
yarn test -- --testNamePattern="kitchen"
```

### Estrutura de Testes

```
test/
├── src/
│   ├── modules/          # Testes unitários por módulo
│   └── test-utils/       # Utilitários de teste
│       ├── factories/    # Factories para dados de teste
│       ├── helpers/      # Helpers para testes
│       ├── matchers/     # Matchers customizados do Jest
│       ├── mocks/        # Mocks para dependências
│       └── setup/        # Configuração global dos testes
└── app.e2e-spec.ts      # Testes end-to-end
```

## 📚 API Endpoints

### Sistema

#### Informações da API

```http
GET /
```

**Resposta:**
```json
{
  "name": "API Kitchen - Vitola Lanches",
  "version": "1.0.0",
  "description": "API para gerenciamento da cozinha do Vitola Lanches",
  "endpoints": {
    "orders": "/kitchen/orders",
    "docs": "/api/docs",
    "health": "/health"
  },
  "features": [
    "Listagem de pedidos da cozinha",
    "Alteração de status dos pedidos",
    "Documentação interativa com Swagger"
  ]
}
```

#### Health Check

```http
GET /health
```

**Resposta:**
```json
{
  "status": "ok",
  "timestamp": "2025-01-12T23:30:00.000Z",
  "uptime": 12345,
  "memory": {
    "rss": 50331648,
    "heapTotal": 20971520,
    "heapUsed": 15728640
  },
  "version": "v18.17.0"
}
```

### Cozinha

#### Listar Pedidos da Cozinha

```http
GET /kitchen/orders
```

**Resposta:**
```json
[
  {
    "_id": "507f1f77bcf86cd799439011",
    "document": "123.456.789-00",
    "orderNumber": 1001,
    "createDate": "2025-01-12T20:00:00.000Z",
    "updateDate": "2025-01-12T20:00:00.000Z",
    "status": "RECEIVED",
    "items": [
      {
        "name": "X-Burger Vitola",
        "description": "Hambúrguer artesanal com carne 180g",
        "quantity": 2,
        "unitPrice": 25.90
      }
    ],
    "total": 51.80
  }
]
```

#### Atualizar Status do Pedido

```http
PUT /kitchen/orders/{orderId}/status
Content-Type: application/json

{
  "status": "PREPARING"
}
```

**Status Disponíveis:**
- `RECEIVED` - Pedido recebido
- `PREPARING` - Em preparação
- `READY` - Pronto para entrega
- `COMPLETED` - Concluído
- `CANCELED` - Cancelado
- `DISCARDED` - Descartado

### Documentação Completa

Acesse a documentação interativa do Swagger em: `http://localhost:3000/api/docs`

## 🚀 CI/CD

Este projeto utiliza GitHub Actions para automação de CI/CD com os seguintes workflows:

### 🔄 Pipeline Principal (ci-cd-pipeline.yml)
- **Lint & Format**: ESLint e Prettier
- **Testes**: Unitários e integração com Node.js 18/20
- **Segurança**: npm audit e Snyk
- **Qualidade**: SonarCloud analysis
- **Build**: Verificação de build
- **Docker**: Build de imagem (main/tags)

### 🐳 Build Docker (build.yml)
- **Multi-arch**: AMD64 e ARM64
- **Testes**: Verificação da imagem
- **Segurança**: Scan com Trivy
- **Registry**: GitHub Container Registry

### 📦 Release (release.yml)
- **Automático**: Em tags `v*.*.*`
- **Deploy**: Staging automático, produção manual
- **Changelog**: Geração automática

### 🧹 Limpeza (cleanup.yml)
- **Semanal**: Domingos às 2h
- **Artefatos**: Remove builds antigos
- **Imagens**: Limpa containers não utilizados

### 📊 Métricas
- **Cobertura**: Mínimo 70%
- **Qualidade**: SonarCloud Quality Gate
- **Segurança**: Scan contínuo de vulnerabilidades

Para mais detalhes, consulte [.github/README.md](.github/README.md).

## 🐳 Docker

### Serviços Disponíveis

- **api-kitchen**: Aplicação NestJS (porta 3000)
- **mongodb**: Banco de dados MongoDB 6.0 (porta 27017)
- **redis**: Cache Redis 7.2 (porta 6379)

### Comandos Úteis

```bash
# Subir apenas o banco de dados
docker-compose up mongodb redis -d

# Ver logs da aplicação
docker-compose logs -f api-kitchen

# Executar comandos dentro do container
docker-compose exec api-kitchen yarn test

# Parar todos os serviços
docker-compose down

# Limpar volumes (cuidado: remove dados)
docker-compose down -v

# Rebuild da aplicação
docker-compose up --build api-kitchen
```

### Configuração de Rede

Os containers estão configurados na rede `mynetwork` para comunicação interna:

```yaml
networks:
  mynetwork:
    driver: bridge
```

## 📊 SonarQube

A API Kitchen inclui configuração completa do SonarQube para análise de qualidade de código.

### Iniciar SonarQube

```bash
# Iniciar container do SonarQube
npm run sonar:start

# Ou usando script avançado
./sonar-manager.sh start
```

### Executar Análise

```bash
# Método simples
npm run sonar:scan

# Método avançado (interativo)
./sonar-manager.sh

# Método manual
npm run test:cov && sonar-scanner
```

### Acessar Relatórios

- **URL**: http://localhost:9001
- **Login**: admin/admin (primeira vez)
- **Dashboard**: http://localhost:9001/dashboard?id=api-kitchen

### Scripts Disponíveis

```bash
npm run sonar:start    # Iniciar SonarQube
npm run sonar:stop     # Parar SonarQube
npm run sonar:scan     # Executar análise
npm run sonar:logs     # Visualizar logs
```

### Configurações

- **Projeto**: api-kitchen
- **Porta**: 9001 (para não conflitar com api-payments)
- **Cobertura**: > 80%
- **Quality Gate**: A em todas as métricas

Para mais detalhes, consulte [SONAR.md](./SONAR.md).

## 📊 Dados de Exemplo

O projeto inclui um arquivo `orders-sample-data.json` com dados de exemplo para popular o banco:

### Estrutura dos Dados

```json
{
  "document": "123.456.789-00",
  "orderNumber": 1001,
  "createDate": "2025-01-12T20:00:00.000Z",
  "updateDate": "2025-01-12T20:00:00.000Z",
  "status": "RECEIVED",
  "items": [
    {
      "name": "X-Burger Vitola",
      "description": "Hambúrguer artesanal com carne 180g",
      "quantity": 2,
      "unitPrice": 25.90
    }
  ],
  "total": 69.30
}
```

### Como Importar os Dados

#### Opção 1: MongoDB Compass
1. Conecte em `***********************************************************`
2. Acesse o banco `lanchonete-db`
3. Vá na collection `Orders`
4. Importe o arquivo `orders-sample-data.json`

#### Opção 2: MongoDB Shell
```bash
mongosh "************************************************************************"
db.Orders.insertMany([/* dados do arquivo JSON */])
```

#### Opção 3: Via Terminal
```bash
mongoimport --uri="************************************************************************" \
  --collection=Orders \
  --file=orders-sample-data.json \
  --jsonArray
```

### Status dos Pedidos de Exemplo

- **RECEIVED**: 1 pedido
- **PREPARING**: 2 pedidos
- **READY**: 1 pedido
- **COMPLETED**: 1 pedido

## 📈 Qualidade de Código

### Linting e Formatação

```bash
# Verificar problemas de lint
yarn lint

# Corrigir problemas automaticamente
yarn lint --fix

# Formatar código
yarn format
```

### Padrões de Código

- **ESLint**: Configuração baseada em @typescript-eslint
- **Prettier**: Formatação automática de código
- **TypeScript**: Tipagem estática rigorosa
- **Conventional Commits**: Padrão de commits semânticos

### Métricas de Qualidade

- **Cobertura de Testes**: > 80%
- **Complexidade Ciclomática**: < 10
- **Duplicação de Código**: < 3%
- **Vulnerabilidades**: 0
- **Code Smells**: Minimizados

## 🔒 Segurança

### Variáveis de Ambiente

- Nunca commite arquivos `.env` com dados sensíveis
- Use `.env.example` como template
- Configure secrets adequadamente em produção

### Validação de Dados

- Todos os DTOs possuem validação com Joi
- Sanitização de inputs
- Tratamento de erros centralizado
- Validação de schemas MongoDB

### Autenticação (Futuro)

O projeto está preparado para implementação de autenticação:

- Estrutura modular para guards
- Preparado para JWT tokens
- Estrutura para roles e permissões

## 🚀 Deploy

### Variáveis de Ambiente - Produção

```env
NODE_ENV=production
PORT=3000
MONGO_URI=mongodb://your-production-mongo-uri/lanchonete-db?authSource=admin
MONGO_INITDB_ROOT_USERNAME=your-production-user
MONGO_INITDB_ROOT_PASSWORD=your-production-password
```

### Build para Produção

```bash
# Build da aplicação
yarn build

# Executar em produção
yarn start:prod
```

### Docker em Produção

```bash
# Build da imagem
docker build -t vitola-lanches-kitchen-api .

# Executar container
docker run -p 3000:3000 \
  -e MONGO_URI=your-mongo-uri \
  -e NODE_ENV=production \
  vitola-lanches-kitchen-api
```

### Considerações de Produção

- Configure adequadamente as variáveis de ambiente
- Use volumes persistentes para MongoDB
- Configure logs estruturados
- Implemente monitoramento de saúde
- Configure backup automático do banco

## 🤝 Contribuição

### Fluxo de Desenvolvimento

1. **Fork** o projeto
2. **Crie** uma branch para sua feature (`git checkout -b feature/nova-feature`)
3. **Commit** suas mudanças (`git commit -m 'feat: adiciona nova feature'`)
4. **Push** para a branch (`git push origin feature/nova-feature`)
5. **Abra** um Pull Request

### Padrões de Commit

Seguimos o padrão [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` Nova funcionalidade
- `fix:` Correção de bug
- `docs:` Documentação
- `style:` Formatação
- `refactor:` Refatoração
- `test:` Testes
- `chore:` Tarefas de build/config

### Checklist para PRs

- [ ] Testes passando (`yarn test`)
- [ ] Lint sem erros (`yarn lint`)
- [ ] Código formatado (`yarn format`)
- [ ] Documentação atualizada
- [ ] Cobertura de testes mantida
- [ ] Swagger atualizado se necessário

## 📝 Licença

Este projeto está sob a licença [UNLICENSED](LICENSE).

## 👥 Equipe

- **Desenvolvedores**:
  - Victor Santos

---

## 🔄 Changelog

### v0.0.1

- ✨ Implementação inicial da API Kitchen
- 🔧 Configuração do ambiente de desenvolvimento
- 🧪 Setup de testes unitários e integração
- 📚 Documentação Swagger
- 🐳 Containerização com Docker
- 🗄️ Configuração MongoDB com Mongoose
- 📊 Sistema de status de pedidos
- 🍔 Módulo completo de gerenciamento da cozinha

---

**🍔 Vitola Lanches - Transformando pedidos em experiências deliciosas!**
