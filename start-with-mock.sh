#!/bin/bash

# Script para iniciar a API Kitchen com o mock da API Orders

set -e

echo "🍔 Starting API Kitchen with Orders Mock..."
echo "=========================================="

# Parar containers existentes
echo "🛑 Stopping existing containers..."
docker compose down

# Limpar build cache se necessário
echo "🧹 Cleaning up..."
docker system prune -f --volumes

# Build e start dos serviços
echo "🏗️  Building and starting services..."
docker compose up --build -d orders-api

# Aguardar o mock da API Orders estar pronto
echo "⏳ Waiting for Orders API Mock to be ready..."
sleep 10

# Verificar se o mock está respondendo
echo "🔍 Checking Orders API Mock health..."
for i in {1..30}; do
    if curl -s http://localhost:3001/health > /dev/null; then
        echo "✅ Orders API Mock is ready!"
        break
    fi
    echo "⏳ Waiting for Orders API Mock... ($i/30)"
    sleep 2
done

# Iniciar a aplicação principal
echo "🚀 Starting API Kitchen..."
docker compose up --build app

echo "🎉 All services are running!"
echo "📋 Available endpoints:"
echo "   API Kitchen: http://localhost:3000"
echo "   Orders Mock: http://localhost:3001"
echo "   SonarQube:   http://localhost:9001"