# Configuração adicional para SonarCloud
# Este arquivo garante que as configurações sejam aplicadas corretamente

# Configurações de cobertura específicas
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.typescript.lcov.reportPaths=coverage/lcov.info

# Configurações de teste
sonar.testExecutionReportPaths=test-report.xml

# Configurações de linguagem
sonar.language=ts
sonar.sources=src
sonar.tests=test

# Exclusões específicas para cobertura
sonar.coverage.exclusions=**/*.spec.ts,**/*.test.ts,**/main.ts,**/test/**

# Configurações de qualidade
sonar.qualitygate.wait=true
